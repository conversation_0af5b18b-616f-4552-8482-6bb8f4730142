package org.etwxr9.frostcraftbu.Module;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.EntityType;
import org.etwxr9.buildingunit.BuildingUnitAPI;
import org.etwxr9.frostcraftbu.Building.FCBuilding;
import org.etwxr9.frostcraftbu.Module.ContainerModule.ContainerModuleSaveData;
import org.etwxr9.frostcraftbu.Module.ContainerModule.ContainerUnit;
import org.etwxr9.frostcraftbu.Module.Behavior.ICharge;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

public class ChargeModule extends BaseModule implements ICharge {

    private Map<String, Location> chargePointMap = new HashMap<>();

    private List<Location> usedChargePointLocations = new ArrayList<>();

    public ChargeModule(FCBuilding building) {
        super(building);
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ChargeModuleSaveData extends BaseModuleSaveData {
        private Map<String, String> chargePointMap;
        private List<String> usedChargePointLocations;

        public Map<String, String> getChargePointMap() {
            return chargePointMap;
        }

        public List<String> getUsedChargePointLocations() {
            return usedChargePointLocations;
        }

        public ChargeModuleSaveData() {
        }

        public ChargeModuleSaveData(Map<String, String> chargePointMap, List<String> usedChargePointUUIDs) {
            this.chargePointMap = chargePointMap;
            this.usedChargePointLocations = usedChargePointUUIDs;
        }

        @Override
        public String getModuleTypeId() {
            return ModuleManager.ModuleType.ChargeModule.getName();
        }

    }

    @Override
    public BaseModuleSaveData getSaveData() {
        // convert location to string
        Map<String, String> chargePointMapS = chargePointMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().toString()));
        List<String> usedChargePointUUIDsS = usedChargePointLocations.stream().map(l -> l.toString())
                .collect(Collectors.toList());
        return new ChargeModuleSaveData(chargePointMapS, usedChargePointUUIDsS);
    }

    @Override
    public void loadSaveData(BaseModuleSaveData data) {
        if (data instanceof ChargeModuleSaveData) {
            ChargeModuleSaveData saveData = (ChargeModuleSaveData) data;
            chargePointMap = new HashMap<>();
            if (saveData.getChargePointMap() != null) {
                saveData.getChargePointMap().forEach((uuid, locS) -> {
                    chargePointMap.put(uuid, locFromString(locS));
                });
            }
            if (saveData.getUsedChargePointLocations() != null) {
                usedChargePointLocations = saveData.getUsedChargePointLocations().stream().map(l -> locFromString(l))
                        .collect(Collectors.toList());
            }
        }
    }

    private Location locFromString(String locS) {

        // 提取 worldName
        String worldName = locS.replaceAll(".*name=([^}]+).*", "$1");

        // 提取 x, y, z
        double x = Double.parseDouble(locS.replaceAll(".*x=([-0-9.]+).*", "$1"));
        double y = Double.parseDouble(locS.replaceAll(".*y=([-0-9.]+).*", "$1"));
        double z = Double.parseDouble(locS.replaceAll(".*z=([-0-9.]+).*", "$1"));

        World world = Bukkit.getWorld(worldName);
        if (world == null) {
            throw new IllegalArgumentException("World not found: " + worldName);
        }

        return new Location(world, x, y, z);
    }

    @Override
    public void onLoad(boolean frist) {
        if (!frist) {
            return;
        }
        var unit = BuildingUnitAPI.getUnit(building.getUnitUUID());
        var bb = unit.getBoundingBox();
        var entities = unit.getWorld().getNearbyEntities(bb);
        var armorStands = entities.stream().filter(e -> {
            return e.getType().equals(EntityType.ARMOR_STAND);
        });
        armorStands.forEach(e -> {
            chargePointMap.put(e.getUniqueId().toString(), e.getLocation());
        });
    }

    @Override
    public void onUnload() {
    }

    @Override
    public String getModuleTypeId() {
        return ModuleManager.ModuleType.ChargeModule.getName();
    }

    @Override
    public Map<String, Location> getChargePoints() {
        return chargePointMap;
    }

    @Override
    public boolean hasAvailableChargePoint() {
        return chargePointMap.size() > usedChargePointLocations.size();
    }

    @Override
    public Location getAvailableChargePoint() {
        return chargePointMap.values().stream().filter(e -> {
            return !usedChargePointLocations.contains(e);
        }).findFirst().orElse(null);
    }

    @Override
    public Location useChargePoint() {
        if (hasAvailableChargePoint()) {
            var loc = getAvailableChargePoint();
            usedChargePointLocations.add(loc);
            return loc;
        } else {
            return null;
        }
    }

    @Override
    public void releaseChargePoint(Location loc) {
        usedChargePointLocations.remove(loc);
    }

}
