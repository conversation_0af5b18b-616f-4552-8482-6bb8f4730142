package org.etwxr9.frostcraftbu.pathfinding.processor;

import de.bsommerfeld.pathetic.api.pathing.processing.Cost;
import de.bsommerfeld.pathetic.api.pathing.processing.NodeCostProcessor;
import de.bsommerfeld.pathetic.api.pathing.processing.context.NodeEvaluationContext;
import de.bsommerfeld.pathetic.api.provider.NavigationPointProvider;
import de.bsommerfeld.pathetic.api.wrapper.PathPosition;
import de.bsommerfeld.pathetic.bukkit.provider.BukkitNavigationPoint;

/**
 * 无人机成本处理器，实现高度偏好权重计算
 * 
 * 成本计算规则：
 * - 偏好高度：2格高度（相对于底部方块）
 * - 允许高度范围：1-3格
 * - 高度偏离偏好值越远，成本越高
 * - 这样无人机会优先选择2格高度的路径，但在需要时可以调整到1格或3格
 */
public class DroneCostProcessor implements NodeCostProcessor {

    // 偏好悬浮高度（相对于地面）
    private static final double PREFERRED_HOVER_HEIGHT = 2.0;
    
    // 高度偏离成本系数
    private static final double HEIGHT_DEVIATION_COST_MULTIPLIER = 5.0;
    
    // 基础移动成本
    private static final double BASE_MOVEMENT_COST = 1.0;

    @Override
    public Cost calculateCostContribution(NodeEvaluationContext context) {
        PathPosition currentPosition = context.getCurrentPathPosition();
        NavigationPointProvider provider = context.getNavigationPointProvider();
        
        // 计算高度偏好成本
        double heightCost = calculateHeightPreferenceCost(currentPosition, provider, context.getEnvironmentContext());
        
        // 计算地形成本
        double terrainCost = calculateTerrainCost(currentPosition, provider, context.getEnvironmentContext());
        
        // 总成本 = 基础成本 + 高度偏好成本 + 地形成本
        double totalCost = BASE_MOVEMENT_COST + heightCost + terrainCost;
        
        return Cost.of(totalCost);
    }

    /**
     * 计算高度偏好成本
     * 距离偏好高度越远，成本越高
     */
    private double calculateHeightPreferenceCost(PathPosition position, NavigationPointProvider provider, 
                                               de.bsommerfeld.pathetic.api.pathing.context.EnvironmentContext environmentContext) {
        // 寻找底部固体方块
        PathPosition groundPosition = findGroundBelow(position, provider, environmentContext);
        
        if (groundPosition == null) {
            // 如果找不到地面，给予高成本惩罚
            return 100.0;
        }

        // 计算相对于地面的高度
        double relativeHeight = position.getY() - groundPosition.getY();
        
        // 计算与偏好高度的偏差
        double heightDeviation = Math.abs(relativeHeight - PREFERRED_HOVER_HEIGHT);
        
        // 偏差越大，成本越高
        return heightDeviation * HEIGHT_DEVIATION_COST_MULTIPLIER;
    }

    /**
     * 计算地形成本
     * 根据下方地面的材质给予不同的成本
     */
    private double calculateTerrainCost(PathPosition position, NavigationPointProvider provider, 
                                      de.bsommerfeld.pathetic.api.pathing.context.EnvironmentContext environmentContext) {
        // 寻找底部固体方块
        PathPosition groundPosition = findGroundBelow(position, provider, environmentContext);
        
        if (groundPosition == null) {
            return 0.0; // 没有地面，不计算地形成本
        }

        BukkitNavigationPoint groundPoint = 
            (BukkitNavigationPoint) provider.getNavigationPoint(groundPosition, environmentContext);
        
        if (groundPoint == null) {
            return 0.0;
        }

        // 根据地面材质给予不同成本
        switch (groundPoint.getMaterial()) {
            case LAVA:
                return 30.0; // 熔岩上方飞行成本很高
            default:
                return 1.0;  // 其他材质默认成本
        }
    }

    /**
     * 寻找指定位置下方的地面（固体方块）
     */
    private PathPosition findGroundBelow(PathPosition position, NavigationPointProvider provider, 
                                       de.bsommerfeld.pathetic.api.pathing.context.EnvironmentContext environmentContext) {
        // 从当前位置向下搜索，最多搜索10格
        for (int i = 0; i <= 10; i++) {
            PathPosition checkPosition = position.subtract(0, i, 0);
            BukkitNavigationPoint navigationPoint = 
                (BukkitNavigationPoint) provider.getNavigationPoint(checkPosition, environmentContext);
            
            if (navigationPoint != null && !navigationPoint.isTraversable()) {
                // 找到固体方块，这就是地面
                return checkPosition;
            }
        }
        
        return null; // 没有找到地面
    }
}
