package org.etwxr9.frostcraftbu.Module;

import java.util.Map;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.util.Vector;
import org.etwxr9.frostcraftbu.FrostCraftBU;
import org.etwxr9.frostcraftbu.Animation.BuildAnimationManager;
import org.etwxr9.frostcraftbu.Building.BuildingManager;
import org.etwxr9.frostcraftbu.Building.FCBuilding;
import org.etwxr9.frostcraftbu.Item.ItemManager;
import org.etwxr9.frostcraftbu.Module.ModuleManager.ModuleType;
import org.etwxr9.frostcraftbu.Tech.TechTreeManager;
import org.etwxr9.frostcraftbu.Module.Behavior.IChestUI;
import org.etwxr9.frostcraftbu.Module.Behavior.IConstruction;
import org.ipvp.canvas.type.ChestMenu;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextDecoration;

/**
 * yml配置：
 * ui: int
 */
public class ConstructionModule extends BaseModule implements IConstruction, IChestUI {

    public ConstructionModule(FCBuilding building) {
        super(building);
        providedItems = new HashMap<>();
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ConstructionModuleSaveData extends BaseModuleSaveData {
        private Map<String, Integer> providedItems; // 已提供的物品
        private String targetBuildingId; // 目标建筑ID

        public ConstructionModuleSaveData() {
        }

        public ConstructionModuleSaveData(Map<String, Integer> providedItems, String targetBuildingId) {
            this.providedItems = providedItems;
            this.targetBuildingId = targetBuildingId;
        }

        public Map<String, Integer> getProvidedItems() {
            return new HashMap<>(providedItems);
        }

        public String getTargetBuildingId() {
            return targetBuildingId;
        }

        @Override
        public String getModuleTypeId() {
            return ModuleType.ConstructionModule.getName();
        }
    }

    private Map<String, Integer> providedItems; // 已提供的物品
    private String targetBuildingId; // 目标建筑ID

    @Override
    public void registerModule() {

    }

    @Override
    public Map<String, Integer> getRequiredItems() {
        var allRequiredItems = BuildingManager.i().getBuildingConfig(targetBuildingId).getConsume();
        var requiredItems = new HashMap<String, Integer>();
        allRequiredItems.forEach((k, v) -> {
            requiredItems.put(k, v - providedItems.getOrDefault(k, 0));
        });
        return requiredItems;
    }

    @Override
    public Map<String, Integer> getProvidedItems() {
        return new HashMap<>(providedItems);
    }

    /**
     * 从玩家背包中收集并提交所需物品
     * 
     * @param p 玩家对象
     * @return 返回实际提交的物品数量Map，key为物品ID，value为提交数量
     */
    public HashMap<String, Integer> playerProvideItem(Player p) {
        // 获取玩家背包内容
        var inventory = p.getInventory();
        var requiredItems = getRequiredItems();
        HashMap<String, Integer> providedMap = new HashMap<>();

        // 遍历玩家背包中的物品
        for (var item : inventory.getContents()) {
            if (item == null || item.getType() == Material.AIR)
                continue;
            // 获取物品ID
            String itemId = ItemManager.getItemId(item);
            if (itemId == null)
                continue;
            // 检查是否是需要的物品
            if (!requiredItems.containsKey(itemId))
                continue;
            int count = item.getAmount();
            // 添加物品到已提供列表
            int accepted = addItem(itemId, count);
            if (accepted <= 0)
                continue;
            // 记录实际提交数量
            // log
            p.sendMessage("playerProvideItem:" + itemId + ":" + accepted);
            providedMap.put(itemId, providedMap.getOrDefault(itemId, 0) + accepted);
            // 从玩家背包中移除已接受的物品
            item.setAmount(count - accepted);
            if (item.getAmount() <= 0) {
                inventory.remove(item);
            }
        }
        // log
        p.sendMessage("playerProvideItem:" + providedMap.toString());
        return providedMap;
    }

    @Override
    public int addItem(String itemId, int count) {
        if (!getRequiredItems().containsKey(itemId)) {
            return 0;
        }
        // log
        FrostCraftBU.i().getLogger().info("addItem:" + itemId + ":" + count);
        int required = getRequiredItems().get(itemId);
        int current = providedItems.getOrDefault(itemId, 0);
        // log
        FrostCraftBU.i().getLogger().info("providedItems:" + providedItems.toString());
        if (required <= 0) {
            return 0;
        }

        int accepted = Math.min(count, required);
        // log
        FrostCraftBU.i().getLogger().info("accepted:" + accepted);

        providedItems.put(itemId, current + accepted);
        // log
        FrostCraftBU.i().getLogger().info("providedItems:" + providedItems.toString());
        return accepted;
    }

    @Override
    public boolean isCompleted() {
        boolean isCompleted = true;
        for (Map.Entry<String, Integer> entry : getRequiredItems().entrySet()) {
            int required = entry.getValue();
            if (required > 0) {
                isCompleted = false;
                break;
            }
        }
        return isCompleted;
    }

    @Override
    public String getTargetBuildingId() {
        return targetBuildingId;
    }

    @Override
    public void finishConstruction() {
        var min = building.getMinLocation();
        var center = building.getCenter();
        center.setY(min.getY());
        var rotate = building.getRotate();
        // log
        // FrostCraftBU.i().getLogger().info("finishConstruction:" + min.toString() +
        // "_" + center.toString() + "_"
        // + rotate);
        BuildAnimationManager.getInstance()
                .playParticleAnimation(min.clone().subtract(new Vector(1, 0, 1)), center)
                .thenRun(
                        () -> {
                            var oriId = building.getBuildingId();
                            BuildingManager.i().removeBuilding(building, false);
                            var newb = BuildingManager.i().build(targetBuildingId, min, rotate);
                            newb.getoriginBuildingIds().add(oriId);
                        });
    }

    @Override
    public void loadSaveData(BaseModuleSaveData data) {
        if (data instanceof ConstructionModuleSaveData) {
            providedItems = ((ConstructionModuleSaveData) data).getProvidedItems();
            targetBuildingId = ((ConstructionModuleSaveData) data).getTargetBuildingId();
        }
    }

    @Override
    public BaseModuleSaveData getSaveData() {
        return new ConstructionModuleSaveData(providedItems, targetBuildingId);
    }

    @Override
    public String getModuleTypeId() {
        return ModuleType.ConstructionModule.getName();
    }

    @Override
    public ItemStack getIcon() {
        ItemStack icon = ItemManager.getItem("constructionUI");

        return icon;
    }

    @Override
    public ChestMenu getUI() {
        var uiRoll = getModuleConfig().getInt("ui");
        var ui = ChestMenu.builder(1).title("工地").build();

        if (!TechTreeManager.i().isBuildingUnlocked(getTargetBuildingId())) {
            // show can not build
            var lockedBtn = ItemManager.getItem("constructionUILocked");
            var meta = lockedBtn.getItemMeta();
            meta.lore(List.of(Component.text("目标建筑" + getTargetBuildingId() + "未解锁！").color(NamedTextColor.RED)
                    .decoration(TextDecoration.ITALIC, false)));
            lockedBtn.setItemMeta(meta);
            ui.getSlot(0).setItem(lockedBtn);
            ui.getSlot(0).setClickHandler((p, info) -> {
                p.sendMessage("§c目标建筑未解锁！");
            });
            return ui;
        }

        // 提供材料按钮
        var provideBtn = ItemManager.getItem("constructionUIProvide");
        var meta = provideBtn.getItemMeta();
        ArrayList<Component> lore = new ArrayList<>();
        lore.add(Component.text("目标建筑: " + getTargetBuildingId()).color(NamedTextColor.WHITE));
        lore.add(Component.text("点击提供材料").color(NamedTextColor.WHITE));
        lore.add(Component.text("所需材料：").color(NamedTextColor.WHITE));
        // log
        // lore.add(Component.text(getRequiredItems().toString()).color(NamedTextColor.WHITE));
        getRequiredItems().forEach((k, v) -> {
            lore.add(Component.text(k + ":" + v).color(NamedTextColor.WHITE));
        });
        meta.lore(lore);
        provideBtn.setItemMeta(meta);
        ui.getSlot(0).setItem(provideBtn);
        ui.getSlot(0).setClickHandler((p, info) -> {
            ui.close();
            if (isCompleted()) {
                p.sendMessage("§c所有建筑材料已集齐！");
                return;
            }
            HashMap<String, Integer> provideResult = playerProvideItem(p);
            if (provideResult.size() == 0) {
                p.sendMessage("§c没有提供任何材料");
                return;
            }
            var provideText = Component.text("已成功提供物品:").color(NamedTextColor.GREEN).toBuilder();
            provideResult.forEach((k, v) -> {
                var itemDisplayName = ItemManager.getItem(k).displayName();
                provideText.append(itemDisplayName).append(Component.text(":" + v)).append(Component.text(","));
            });
            p.sendMessage(provideText.build());
            // 获取当前需要的物品
            var requiredItems = getRequiredItems();
            if (isCompleted()) {
                p.sendMessage("§a所有建筑材料已集齐！");
                finishConstruction();
            } else {
                var requiredText = Component.text("仍需以下材料：").color(NamedTextColor.RED).toBuilder();
                requiredItems.forEach((itemId, amount) -> {
                    var itemName = ItemManager.getItem(itemId).displayName();
                    requiredText.append(itemName).append(Component.text(":" + amount)).append(Component.text(","));
                });
                p.sendMessage(requiredText.build());
            }

        });

        // 返还材料按钮
        var returnBtn = ItemManager.getItem("constructionUIReturn");
        var metaReturn = returnBtn.getItemMeta();
        ArrayList<Component> loreReturn = new ArrayList<>();
        loreReturn.add(Component.text("目标建筑: " + getTargetBuildingId()).color(NamedTextColor.WHITE));
        loreReturn.add(Component.text("点击返还材料").color(NamedTextColor.WHITE));
        metaReturn.lore(loreReturn);
        returnBtn.setItemMeta(metaReturn);
        ui.getSlot(1).setItem(returnBtn);
        ui.getSlot(1).setClickHandler((p, info) -> {
            ui.close();
            // 返还所有已提供的物品
            providedItems.forEach((k, v) -> {
                var item = ItemManager.getItem(k);
                item.setAmount(v);
                p.getWorld().dropItemNaturally(p.getLocation(), item);
            });
            providedItems.clear();
            p.sendMessage("§a已返还已投入的建筑材料！");
        });

        return ui;
    }

    @Override
    public void setTargetBuildingId(String id) {
        targetBuildingId = id;
    }

    @Override
    public void onLoad(boolean frist) {
        if (!frist) {
            return;
        }
        var upgrade = getModuleConfig().getString("upgradeBuildingId");
        if (upgrade != null) {
            targetBuildingId = upgrade;
        }
    }

    @Override
    public void onUnload() {
    }
}
