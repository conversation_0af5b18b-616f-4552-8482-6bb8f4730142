package org.etwxr9.frostcraftbu.Manager;

import org.bukkit.Bukkit;
import org.etwxr9.frostcraftbu.FrostCraftBU;
import org.etwxr9.frostcraftbu.Building.BuildingManager;
import org.etwxr9.frostcraftbu.Module.Behavior.IProduction;
import org.etwxr9.frostcraftbu.Module.Behavior.IResearch;
import org.etwxr9.frostcraftbu.Module.Behavior.IResourceConsumption;
import org.etwxr9.frostcraftbu.Module.Behavior.IResourceGathering;

public class BuildingTickManager {
    private static BuildingTickManager instance;
    
    public static BuildingTickManager i() {
        if (instance == null) {
            instance = new BuildingTickManager();
        }
        return instance;
    }

    public void start() {
        Bukkit.getScheduler().runTaskTimer(FrostCraftBU.i(), () -> {
            for (var building : BuildingManager.i().getAllBuildings()) {
                for (var module : building.getModules().values()) {
                    if (module instanceof IProduction) {
                        ((IProduction) module).tick();
                    }
                    if (module instanceof IResourceGathering) {
                        ((IResourceGathering) module).tick();
                    }
                    if (module instanceof IResearch) {
                        ((IResearch) module).tick();
                    }
                    if (module instanceof IResourceConsumption) {
                        ((IResourceConsumption) module).tick();
                    }
                }
            }
        }, 1L, 1L);
    }
}


