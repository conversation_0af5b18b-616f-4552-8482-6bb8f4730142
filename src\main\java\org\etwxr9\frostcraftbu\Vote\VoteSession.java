package org.etwxr9.frostcraftbu.Vote;

import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitTask;
import org.etwxr9.frostcraftbu.FrostCraftBU;
import net.md_5.bungee.api.chat.ClickEvent;
import net.md_5.bungee.api.chat.ComponentBuilder;
import net.md_5.bungee.api.chat.HoverEvent;
import net.md_5.bungee.api.chat.TextComponent;
import net.md_5.bungee.api.chat.hover.content.Text;

import java.time.Instant;
import java.util.HashSet;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

public class VoteSession {
    private String id;
    private String title;
    private String description;
    private String creator;
    private long startTime;
    private long endTime;
    private Set<UUID> approveVotes;
    private Set<UUID> rejectVotes;
    private boolean isActive;
    private VoteResult result;
    private double approveThreshold;
    private double rejectThreshold;
    private BukkitTask broadcastTask;
    private CompletableFuture<VoteResult> future;

    public enum VoteResult {
        APPROVED, REJECTED, TIMEOUT, CANCELLED
    }

    public VoteSession(String title, String description, Player creator, long timeoutSeconds) {
        this.id = UUID.randomUUID().toString().substring(0, 8);
        this.title = title;
        this.description = description;
        this.creator = creator.getName();
        this.startTime = Instant.now().getEpochSecond();
        this.endTime = startTime + timeoutSeconds;
        this.approveVotes = new HashSet<>();
        this.rejectVotes = new HashSet<>();
        this.isActive = true;
        this.approveThreshold = 0.5; // 默认50%通过
        this.rejectThreshold = 0.5; // 默认50%拒绝
        this.future = new CompletableFuture<>();
        
        // 开始广播任务
        startBroadcastTask();
    }

    private void startBroadcastTask() {
        broadcastTask = Bukkit.getScheduler().runTaskTimer(
            FrostCraftBU.i(),
            this::broadcast,
            0L,
            20L * 30 // 每30秒广播一次
        );
    }

    public void broadcast() {
        if (!isActive) return;
        
        // 计算剩余时间（秒）
        long remainingTime = endTime - Instant.now().getEpochSecond();
        if (remainingTime < 0) {
            checkStatus(); // 如果超时，检查状态并结束投票
            return;
        }
        
        // 构建消息组件
        TextComponent message = new TextComponent("§e§l投票: §f" + title + "\n");
        TextComponent descComp = new TextComponent("§7" + description + "\n");
        message.addExtra(descComp);
        
        TextComponent timeComp = new TextComponent("§7剩余时间: §f" + formatTime(remainingTime) + " | ");
        timeComp.addExtra("§7赞成: §a" + approveVotes.size() + "§r| §7反对: §c" + rejectVotes.size() + "\n");
        message.addExtra(timeComp);
        
        // 添加投票按钮
        TextComponent approveButton = new TextComponent("§a[赞成]");
        approveButton.setClickEvent(new ClickEvent(ClickEvent.Action.RUN_COMMAND, "/frostcraftbu vote approve " + id));
        approveButton.setHoverEvent(new HoverEvent(HoverEvent.Action.SHOW_TEXT, new Text("点击投赞成票")));
        
        TextComponent rejectButton = new TextComponent(" §c[反对]");
        rejectButton.setClickEvent(new ClickEvent(ClickEvent.Action.RUN_COMMAND, "/frostcraftbu vote reject " + id));
        rejectButton.setHoverEvent(new HoverEvent(HoverEvent.Action.SHOW_TEXT, new Text("点击投反对票")));
        
        message.addExtra(approveButton);
        message.addExtra(rejectButton);
        
        // 广播给所有在线玩家
        for (Player player : Bukkit.getOnlinePlayers()) {
            player.spigot().sendMessage(message);
        }
    }

    public boolean vote(Player player, boolean approve) {
        if (!isActive) {
            player.sendMessage("§c该投票已结束！");
            return false;
        }
        
        UUID playerId = player.getUniqueId();
        
        // 检查玩家是否已投票
        if (approveVotes.contains(playerId) || rejectVotes.contains(playerId)) {
            // 移除已有的票
            approveVotes.remove(playerId);
            rejectVotes.remove(playerId);
        }
        
        // 添加新票
        if (approve) {
            approveVotes.add(playerId);
            player.sendMessage("§a你投了赞成票！");
        } else {
            rejectVotes.add(playerId);
            player.sendMessage("§c你投了反对票！");
        }
        
        // 检查是否满足结束条件
        checkStatus();
        return true;
    }

    public boolean checkStatus() {
        if (!isActive) return false;
        
        int totalPlayers = Bukkit.getOnlinePlayers().size();
        if (totalPlayers == 0) return false;
        
        // 检查是否超时
        if (Instant.now().getEpochSecond() > endTime) {
            end(VoteResult.TIMEOUT);
            return true;
        }
        
        // 检查是否达到通过阈值
        double approveRatio = (double) approveVotes.size() / totalPlayers;
        if (approveRatio >= approveThreshold) {
            end(VoteResult.APPROVED);
            return true;
        }
        
        // 检查是否达到拒绝阈值
        double rejectRatio = (double) rejectVotes.size() / totalPlayers;
        if (rejectRatio >= rejectThreshold) {
            end(VoteResult.REJECTED);
            return true;
        }
        
        return false;
    }

    public void end(VoteResult result) {
        if (!isActive) return;
        
        this.isActive = false;
        this.result = result;
        
        // 取消广播任务
        if (broadcastTask != null) {
            broadcastTask.cancel();
        }
        
        // 广播结果
        String resultMessage = "§e§l投票结束：" + title;
        String resultDetail = "";
        
        switch (result) {
            case APPROVED:
                resultDetail = "§a通过！(赞成: " + approveVotes.size() + ", 反对: " + rejectVotes.size() + ")";
                break;
            case REJECTED:
                resultDetail = "§c拒绝！(赞成: " + approveVotes.size() + ", 反对: " + rejectVotes.size() + ")";
                break;
            case TIMEOUT:
                resultDetail = "§7超时！(赞成: " + approveVotes.size() + ", 反对: " + rejectVotes.size() + ")";
                break;
            case CANCELLED:
                resultDetail = "§7已取消！";
                break;
        }
        
        for (Player player : Bukkit.getOnlinePlayers()) {
            player.sendMessage(resultMessage);
            player.sendMessage(resultDetail);
        }
        
        // 完成异步结果
        future.complete(result);
    }

    private String formatTime(long seconds) {
        long minutes = seconds / 60;
        long remainingSeconds = seconds % 60;
        return String.format("%d分%d秒", minutes, remainingSeconds);
    }

    // Getters
    public String getId() {
        return id;
    }

    public String getTitle() {
        return title;
    }

    public String getDescription() {
        return description;
    }

    public String getCreator() {
        return creator;
    }

    public long getStartTime() {
        return startTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public Set<UUID> getApproveVotes() {
        return approveVotes;
    }

    public Set<UUID> getRejectVotes() {
        return rejectVotes;
    }

    public boolean isActive() {
        return isActive;
    }

    public VoteResult getResult() {
        return result;
    }

    public CompletableFuture<VoteResult> getFuture() {
        return future;
    }
} 