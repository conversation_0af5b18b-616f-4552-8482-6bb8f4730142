package org.etwxr9.frostcraftbu.Cmd;

import org.bukkit.ChatColor;
import org.bukkit.entity.Player;
import org.etwxr9.frostcraftbu.Tech.TechTreeManager;

import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class CmdResearch implements SubCmdBase {

    @Override
    public boolean onSubCommand(Player p, String[] args) {
        if (!p.hasPermission("frostcraftbu.command.research")) {
            p.sendMessage(Component.text("You don't have permission to use this command.").color(NamedTextColor.RED));
            return true;
        }

        if (args.length == 0) {
            sendHelp(p);
            return true;
        }

        String subCommand = args[0].toLowerCase();
        TechTreeManager manager = TechTreeManager.i();

        switch (subCommand) {
            case "set":
                if (args.length < 2) {
                    p.sendMessage(
                            Component.text("Usage: /fcbu research set <techName|none>").color(NamedTextColor.RED));
                    return true;
                }
                String techToSet = args[1];
                if (techToSet.equalsIgnoreCase("none")) {
                    manager.setCurrentResearchProject(null);
                    p.sendMessage(Component.text("Stopped current research project.").color(NamedTextColor.GREEN));
                } else {
                    if (manager.setCurrentResearchProject(techToSet)) {
                        p.sendMessage(Component.text("Set current research project to: " + techToSet)
                                .color(NamedTextColor.GREEN));
                        p.sendMessage(Component.text("Required materials: "
                                + manager.getTechByName(techToSet).getConsume().toString())
                                .color(NamedTextColor.YELLOW)); // Assumes getTechByName
                        // exists or is added
                        p.sendMessage(Component.text("Required dependencies: "
                                + manager.getTechByName(techToSet).getDependencies().toString())
                                .color(NamedTextColor.YELLOW)); // Assumes
                        // getTechByName
                        // exists or is added
                    } else {
                        p.sendMessage(Component.text("Failed to set research project. Tech '" + techToSet
                                + "' not found or invalid.").color(NamedTextColor.RED));
                    }
                }
                break;

            case "add":
                if (args.length < 3) {
                    p.sendMessage(Component.text("Usage: /fcbu research add <materialName> <amount>")
                            .color(NamedTextColor.RED));
                    return true;
                }
                String materialName = args[1];
                int amount;
                try {
                    amount = Integer.parseInt(args[2]);
                    if (amount <= 0) {
                        p.sendMessage(Component.text("Amount must be positive.").color(NamedTextColor.RED));
                        return true;
                    }
                } catch (NumberFormatException e) {
                    p.sendMessage(Component.text("Invalid amount: " + args[2]).color(NamedTextColor.RED));
                    return true;
                }
                if (manager.addResearchMaterial(materialName, amount)) {
                    p.sendMessage(Component.text("Research completed by adding materials!").color(NamedTextColor.GOLD));
                } else {
                    p.sendMessage(Component.text("Added " + amount + " " + materialName + " to the research pool.")
                            .color(NamedTextColor.GREEN));
                }
                break;

            case "unlock":
                if (args.length < 2) {
                    p.sendMessage(Component.text("Usage: /fcbu research unlock <techName>").color(NamedTextColor.RED));
                    return true;
                }
                String techToUnlock = args[1];
                if (manager.forceUnlock(techToUnlock)) {
                    p.sendMessage(Component.text("Successfully unlocked tech ly: " + techToUnlock)
                            .color(NamedTextColor.GREEN));
                } else {
                    p.sendMessage(
                            Component.text("Failed to unlock tech. It might not exist or is already unlocked ly.")
                                    .color(NamedTextColor.RED));
                }
                break;

            case "status":
                String currentProject = manager.getCurrentResearchProject();
                Map<String, Integer> materials = manager.getResearchMaterials();
                List<String> Unlocks = manager.getUnlockedTechs();

                p.sendMessage(Component.text("--- Research Status ---").color(NamedTextColor.AQUA));
                p.sendMessage(
                        Component.text("Current Project: " + (currentProject != null ? currentProject : "None"))
                                .color(NamedTextColor.YELLOW));
                if (currentProject != null && manager.getTechByName(currentProject) != null) {
                    p.sendMessage(Component.text("  Required: "
                            + manager.getTechByName(currentProject).getConsume().toString())
                            .color(NamedTextColor.GRAY));
                    p.sendMessage(Component.text("  Dependencies: "
                            + manager.getTechByName(currentProject).getDependencies().toString())
                            .color(NamedTextColor.GRAY));
                }
                p.sendMessage(
                        Component.text("Current Materials: " + materials.toString()).color(NamedTextColor.YELLOW));
                p.sendMessage(Component.text("ly Unlocked Techs: " + Unlocks.toString())
                        .color(NamedTextColor.YELLOW));
                p.sendMessage(Component.text("---------------------").color(NamedTextColor.AQUA));
                break;

            case "lock":
                if (args.length < 2) {
                    p.sendMessage(Component.text("用法: /fcbu research lock <techName>").color(NamedTextColor.RED));
                    return true;
                }
                String techToLock = args[1];
                if (manager.lockTech(techToLock)) {
                    p.sendMessage(Component.text("成功锁定科技: " + techToLock).color(NamedTextColor.GREEN));
                } else {
                    p.sendMessage(Component.text("无法锁定科技。科技 '" + techToLock + "' 不存在或未被解锁。")
                            .color(NamedTextColor.RED));
                }
                break;

            case "lockall":
                if (!p.hasPermission("frostcraftbu.command.research.lockall")) {
                    p.sendMessage(Component.text("你没有权限使用此命令。").color(NamedTextColor.RED));
                    return true;
                }
                int lockedCount = manager.lockAllTechs();
                if (lockedCount > 0) {
                    p.sendMessage(Component.text("成功锁定所有科技，共 " + lockedCount + " 个。").color(NamedTextColor.GREEN));
                } else {
                    p.sendMessage(Component.text("没有已解锁的科技可以锁定。").color(NamedTextColor.YELLOW));
                }
                break;

            default:
                sendHelp(p);
                break;
        }

        return true;
    }

    @Override
    public List<String> onTabComplete(Player p, String[] args) {
        if (!p.hasPermission("frostcraftbu.command.research")) {
            return null;
        }

        if (args.length == 1) {
            return List.of("set", "add", "unlock", "lock", "lockall", "status").stream()
                    .filter(s -> s.startsWith(args[0].toLowerCase()))
                    .collect(Collectors.toList());
        }

        TechTreeManager manager = TechTreeManager.i();

        if (args.length == 2) {
            String subCommand = args[0].toLowerCase();
            if (subCommand.equals("set") || subCommand.equals("unlock")) {
                // Suggest available tech names + "none" for set
                List<String> suggestions = new ArrayList<>(manager.getTechs().keySet());
                if (subCommand.equals("set")) {
                    suggestions.add("none");
                }
                return suggestions.stream()
                        .filter(s -> s.toLowerCase().startsWith(args[1].toLowerCase()))
                        .collect(Collectors.toList());
            }
            if (subCommand.equals("lock")) {
                // 为lock命令提供已解锁的科技名称
                return manager.getUnlockedTechs().stream()
                        .filter(s -> s.toLowerCase().startsWith(args[1].toLowerCase()))
                        .collect(Collectors.toList());
            }
            if (subCommand.equals("add")) {
                // Suggest known material names? Or leave blank? For now, blank.
                return List.of("<materialName>");
            }
        }

        if (args.length == 3) {
            String subCommand = args[0].toLowerCase();
            if (subCommand.equals("add")) {
                return List.of("<amount>");
            }
        }

        return null; // No suggestions
    }

    private void sendHelp(Player p) {
        p.sendMessage(Component.text("--- Research Command Help ---").color(NamedTextColor.AQUA));
        p.sendMessage(Component.text("/fcbu research set <techName|none>" + ChatColor.WHITE
                + " - Set the current  research project.").color(NamedTextColor.GOLD));
        p.sendMessage(Component.text("/fcbu research add <materialName> <amount>" + ChatColor.WHITE
                + " - Add materials to the  pool.").color(NamedTextColor.GOLD));
        p.sendMessage(Component.text("/fcbu research unlock <techName>" + ChatColor.WHITE
                + " - Force unlock a tech ly (Admin).").color(NamedTextColor.GOLD));
        p.sendMessage(Component.text("/fcbu research lock <techName>" + ChatColor.WHITE
                + " - 重新锁定指定科技 (Admin).").color(NamedTextColor.GOLD));
        p.sendMessage(Component.text("/fcbu research lockall" + ChatColor.WHITE
                + " - 重新锁定所有科技 (Admin).").color(NamedTextColor.GOLD));
        p.sendMessage(Component.text("/fcbu research status" + ChatColor.WHITE + " - Show current research status.")
                .color(NamedTextColor.GOLD));
        p.sendMessage(Component.text("---------------------------").color(NamedTextColor.AQUA));
    }

    // Helper method to get tech details - needs to be added to TechTreeManager
    // Add these methods to TechTreeManager.java:
    /*
     * public Tech getTechByName(String name) {
     * return techs.get(name);
     * }
     * 
     * public List<String> getAllTechNames() {
     * return new ArrayList<>(techs.keySet());
     * }
     */

}
