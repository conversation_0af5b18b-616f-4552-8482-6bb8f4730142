package org.etwxr9.frostcraftbu.Listener;

import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.block.BlockFace;
import org.bukkit.block.Sign;
import org.bukkit.block.sign.Side;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.event.inventory.InventoryAction;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.util.Vector;
import org.etwxr9.buildingunit.BuildingUnitAPI;
import org.etwxr9.frostcraftbu.FrostCraftBU;
import org.etwxr9.frostcraftbu.IconId;
import org.etwxr9.frostcraftbu.Animation.BuildAnimationManager;
import org.etwxr9.frostcraftbu.Building.BuildingManager;
import org.etwxr9.frostcraftbu.Building.FCBuilding;
import org.etwxr9.frostcraftbu.Item.ItemManager;
import org.etwxr9.frostcraftbu.Module.ConstructionModule;
import org.etwxr9.frostcraftbu.Module.ModuleManager;
import org.etwxr9.frostcraftbu.Module.Behavior.IChestUI;
import org.etwxr9.frostcraftbu.Module.ModuleManager.ModuleType;
import org.etwxr9.frostcraftbu.Tech.TechTreeManager;
import org.etwxr9.frostcraftbu.Vote.VoteManager;
import org.etwxr9.frostcraftbu.Vote.VoteSession;
import org.ipvp.canvas.slot.ClickOptions;
import org.ipvp.canvas.type.ChestMenu;
import org.jetbrains.annotations.NotNull;

import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class BuildSignListener implements Listener {

    private HashMap<Location, Location> buildingList = new HashMap<>();

    private final static int BUILDING_DISPLAY_INTERVAL = 2;

    private final static int BUILDING_DISPLAY_PATH_LENGTH = 10;

    @EventHandler
    public void OnRClickSign(PlayerInteractEvent e) {
        if (e.getAction() != Action.RIGHT_CLICK_BLOCK)
            return;
        var p = e.getPlayer();
        var signBlock = e.getClickedBlock();
        if (!(signBlock.getState() instanceof Sign))
            return;

        var signState = (Sign) signBlock.getState();
        // 判断建设牌子内容
        String[] signData = signState.getSide(Side.FRONT).getLines();

        // 判断是否是空地建设牌子
        if (signData[0].equals(FrostCraftBU.i().getConfig().getString("EmptyLandSign0"))) {
            e.setCancelled(true);
            // 获取牌子方向
            var signBlockData = (org.bukkit.block.data.type.Sign) signState.getBlockData();
            BlockFace signDirection = signBlockData.getRotation();
            // 建设按钮并更新牌子
            if (buildingList.containsKey(signBlock.getLocation())) {
                p.sendMessage(FrostCraftBU.i().getConfig().getString("AlreadyBuilding"));
                return;
            }
            // 检测是否已经存在建筑
            if (BuildingManager.i().getBuildingAt(signBlock.getLocation().clone().add(new Vector(1, 0, 1))) != null) {
                p.sendMessage(FrostCraftBU.i().getConfig().getString("AlreadyHaveBuilding"));
                return;
            }
            String ConsSiteType = "";
            var landTypeMap = FrostCraftBU.i().getConfig().getConfigurationSection("EmptyLandSign2List");
            if (signData.length < 3) {
                ConsSiteType = "小工地";
            } else {
                ConsSiteType = landTypeMap.getString(signData[2], "小工地");
            }
            final String finalConsSiteType = ConsSiteType;
            // 生成空地建设菜单
            var emptyBuildMenu = ChestMenu.builder(6)
                    .title("选择建筑进行建设")
                    .build();
            // 读取building.getYml里的建筑数据
            final int[] slotIndexContainer = { 0 };
            // 筛选出所有的升级建筑的id
            ArrayList<String> upgradeBuildingIds = new ArrayList<>();
            BuildingManager.i().getBuildingConfigMap().values().stream()
                    .filter(buildingConfig -> buildingConfig.getAllModuleConfig()
                            .containsKey(ModuleType.ConstructionModule.getName())
                            && buildingConfig.getModuleConfig(ModuleType.ConstructionModule.getName())
                                    .contains("upgradeBuildingId"))
                    .forEach(buildingConfig -> {
                        upgradeBuildingIds.add(buildingConfig.getModuleConfig(ModuleType.ConstructionModule.getName())
                                .getString("upgradeBuildingId"));
                    });

            BuildingManager.i().getBuildingConfigMap().forEach((k, v) -> {
                var buildingId = k;
                if (!TechTreeManager.i().isBuildingUnlocked(buildingId)) {
                    return;
                }
                if (upgradeBuildingIds.contains(buildingId)) {
                    return;
                }
                var slotIndex = slotIndexContainer[0];
                var buildingConfig = BuildingManager.i().getBuildingConfig(buildingId);
                var buildingName = buildingConfig.getName();
                var buildingLandType = buildingConfig.getConsSiteType();

                if (buildingLandType == null) {
                    return;
                }
                if (!buildingLandType.equals(finalConsSiteType))
                    return;
                emptyBuildMenu.getSlot(slotIndex).setClickOptions(
                        ClickOptions.builder().allow(ClickType.LEFT).allow(InventoryAction.PICKUP_ONE).build());
                var btnItem = ItemManager.getItem(buildingConfig.getIconItemId());
                // 添加材料消耗
                var lore = btnItem.lore();
                var consume = buildingConfig.getConsume();
                if (consume != null) {
                    lore.add(Component.text("材料消耗:").color(NamedTextColor.WHITE));
                    consume.forEach((itemId, count) -> {
                        var itemName = ItemManager.getItem(itemId).displayName();
                        lore.add(Component.text("  - ").append(itemName).append(Component.text(" x" + count))
                                .color(NamedTextColor.WHITE));
                    });
                }
                btnItem.lore(lore);
                emptyBuildMenu.getSlot(slotIndex).setItem(btnItem);
                // 点击后，粘贴建筑，并判断建筑TYPE，生成对应类，改变牌子内容
                emptyBuildMenu.getSlot(slotIndex).setClickHandler((player, info) -> {
                    // 关闭菜单
                    emptyBuildMenu.close(player);
                    // open another menu to select rotation
                    var rotationMenu = ChestMenu.builder(1)
                            .title("选择建筑旋转")
                            .build();
                    HashMap<Integer, IconId> rotationMap = new HashMap<>();
                    rotationMap.put(0, IconId.BUILD_SOUTH);
                    rotationMap.put(1, IconId.BUILD_EAST);
                    rotationMap.put(2, IconId.BUILD_NORTH);
                    rotationMap.put(3, IconId.BUILD_WEST);
                    // 检查是否是不可破坏建筑，确定为牌子方向
                    var unableToDestroyList = FrostCraftBU.i().getConfig().getStringList("unableToDestroyList");
                    if (unableToDestroyList.contains(finalConsSiteType)) {
                        rotationMap.clear();
                        switch (signDirection) {
                            // 缩减到四个方向
                            case NORTH, NORTH_WEST, NORTH_EAST:
                                rotationMap.put(0, IconId.BUILD_SOUTH);
                                break;
                            case EAST, EAST_NORTH_EAST, EAST_SOUTH_EAST:
                                rotationMap.put(1, IconId.BUILD_EAST);
                                break;
                            case SOUTH, SOUTH_WEST, SOUTH_EAST:
                                rotationMap.put(2, IconId.BUILD_NORTH);
                                break;
                            case WEST, WEST_NORTH_WEST, WEST_SOUTH_WEST:
                                rotationMap.put(3, IconId.BUILD_WEST);
                                break;
                            default:
                                rotationMap.put(0, IconId.BUILD_SOUTH);
                                break;
                        }
                    }
                    rotationMap.forEach((rotation, iconId) -> {
                        rotationMenu.getSlot(rotation).setClickOptions(
                                ClickOptions.builder().allow(ClickType.LEFT).allow(InventoryAction.PICKUP_ONE).build());
                        rotationMenu.getSlot(rotation)
                                .setItem(ItemManager.getItem(iconId.toString().toLowerCase()));
                        rotationMenu.getSlot(rotation).setClickHandler((player2, info2) -> {
                            rotationMenu.close(player2);
                            handleBuildingConstruction(player2, buildingId, signBlock, signState, buildingName,
                                    rotation,
                                    finalConsSiteType);
                        });
                    });
                    rotationMenu.open(player);
                });
                slotIndexContainer[0]++;
            });
            emptyBuildMenu.open(p);
        }
        // 判断是否是已建设建筑牌子
        else if (signData[0].equals(FrostCraftBU.i().getConfig().getString("OccupiedLandSign0"))) {
            e.setCancelled(true);
            var building = BuildingManager.i()
                    .getBuildingAt(signBlock.getLocation());
            if (building == null) {
                // 旧位置检测
                building = BuildingManager.i()
                        .getBuildingAt(signBlock.getLocation().clone().add(new Vector(1, 0, 1)));
                if (building == null) {
                    p.sendMessage("建筑不存在");
                    return;
                }
            }
            openManageUI(p, building, signBlock.getLocation());
        }
    }

    /**
     * 处理建筑建设
     * 
     * @param player
     * @param buildingId
     * @param signBlock
     * @param signState
     * @param buildingName
     * @param rotation
     * @param landTypeBuildingId
     */
    private void handleBuildingConstruction(Player player, String buildingId, Block signBlock, Sign signState,
            String buildingName, int rotation, String landTypeBuildingId) {
        // 更新玩家背包防止物品复制bug
        new BukkitRunnable() {
            @Override
            public void run() {
                player.updateInventory();
            }
        }.runTaskLater(FrostCraftBU.i(), 1);

        var buildingConfig = BuildingManager.i().getBuildingConfig(landTypeBuildingId);
        @NotNull
        Location signLoc = signBlock.getLocation();
        var centerLoc = signLoc.clone();
        buildingList.put(signLoc, centerLoc);
        int voteTimeout = FrostCraftBU.i().getConfig().getInt("voteTimeout", 60);
        VoteSession session = VoteManager.i().createVote(player, "建设投票",
                player.getName() + "发起了建设建筑:" + buildingId + "的投票，请在" + voteTimeout + "秒内投出你的意见。", voteTimeout);
        // player.sendMessage("§a投票已创建！ID: " + session.getId());
        session.getFuture().thenApply(result -> {
            if (result == VoteSession.VoteResult.APPROVED) {
                // 公告
                Bukkit.broadcast(Component.text("§a投票通过！建设建筑工地: " + buildingId).color(NamedTextColor.GREEN));
                // 播放粒子动画
                BuildAnimationManager.getInstance().playParticleAnimation(signLoc, centerLoc)
                        .thenRun(() -> {
                            buildingList.remove(signLoc);
                            // 粘贴建筑
                            int radius = (int) (buildingConfig.getSize().getX() / 2);
                            var minLocation = signLoc.clone().subtract(new Vector(radius, 0, radius));
                            var building = BuildingManager.i().build(landTypeBuildingId, minLocation, rotation);
                            if (building == null) {
                                player.sendMessage("未知原因建设失败！");
                                return;
                            }
                            if (building.getModule("construction") instanceof ConstructionModule) {
                                ConstructionModule consModule = (ConstructionModule) building.getModule("construction");
                                consModule.setTargetBuildingId(buildingId);
                            } else {
                                player.sendMessage("建筑" + building.getBuildingId() + "没有工地模块！");
                            }
                            // 检查牌子位置是否还有牌子（可能被建筑覆盖，也可能留下，确保牌子被摧毁）、
                            if (signLoc.getBlock().getState() instanceof Sign) {
                                var remainSign = (Sign) signLoc.getBlock().getState();
                                if (remainSign.getSide(Side.FRONT).getLine(0)
                                        .equals(FrostCraftBU.i().getConfig().getString("EmptyLandSign0"))) {
                                    remainSign.setType(Material.AIR);
                                }
                            }
                        });
            } else if (result == VoteSession.VoteResult.REJECTED) {
                Bukkit.broadcast(Component.text("§c投票未通过！取消建设建筑工地: " + buildingId).color(NamedTextColor.RED));
            } else if (result == VoteSession.VoteResult.TIMEOUT) {
                Bukkit.broadcast(Component.text("§7投票超时！取消建设建筑工地: " + buildingId).color(NamedTextColor.GRAY));
            } else if (result == VoteSession.VoteResult.CANCELLED) {
                Bukkit.broadcast(Component.text("§7投票被取消！取消建设建筑工地: " + buildingId).color(NamedTextColor.GRAY));
            }
            return null;
        });
        session.vote(player, true);

    }

    public static void openManageUI(Player p, FCBuilding building, Location signLoc) {
        // 打开已建设建筑菜单,包括拆除按钮
        var occupiedBuildMenu = ChestMenu.builder(1)
                .title("已建设建筑菜单")
                .build();
        // 获取建筑的LandType
        var consSiteType = building.getBuildingConfig().getConsSiteType();

        var landTypeMap = FrostCraftBU.i().getConfig().getConfigurationSection("EmptyLandSign2List");
        if (consSiteType == null) {
            if (landTypeMap.getValues(false).values().contains(building.getBuildingId())) {
                consSiteType = building.getBuildingId();
            } else {
                consSiteType = "小工地";
            }
        }
        final String finalConsSiteType = consSiteType;
        var landTypeName = landTypeMap
                .getValues(false).keySet().stream().filter(s -> landTypeMap.getString(s).equals(finalConsSiteType))
                .findFirst().orElseGet(() -> "小地皮"); // 反向获取key值，获取landType对应的key值，即地皮类型名称。
        occupiedBuildMenu.getSlot(0).setClickOptions(
                ClickOptions.builder().allow(ClickType.LEFT).allow(InventoryAction.PICKUP_ONE).build());
        var unableToDestroyList = FrostCraftBU.i().getConfig().getStringList("unableToDestroyList");
        var slotIndex = new int[] { 0 };
        // log
        // FrostCraftBU.i().getLogger().info("unableToDestroyList: " +
        // unableToDestroyList.toString());
        if (!unableToDestroyList.contains(landTypeName) || p.isOp()) {
            // 获取当前建筑升级前ID以及计算总共应当返回材料
            var allIds = new ArrayList<String>();
            allIds.addAll(building.getoriginBuildingIds());
            allIds.add(building.getBuildingId());
            var totalReturnItems = new HashMap<String, Integer>();
            var returnHalfList = FrostCraftBU.i().getConfig().getStringList("ReturnResources.Half");
            var returnFullList = FrostCraftBU.i().getConfig().getStringList("ReturnResources.Full");

            allIds.forEach(id -> {
                var originBuildingConfig = BuildingManager.i().getBuildingConfig(id);
                var consume = originBuildingConfig.getConsume();
                consume.forEach((itemId, count) -> {
                    if (returnHalfList.contains(itemId)) {
                        totalReturnItems.merge(itemId, count / 2, Integer::sum);
                    } else if (returnFullList.contains(itemId)) {
                        totalReturnItems.merge(itemId, count, Integer::sum);
                    }
                });
                // 单独处理工地已填充材料的返还
                if (building.getModules().containsKey(ModuleManager.ModuleType.ConstructionModule.getName())) {
                    var consModule = (ConstructionModule) building
                            .getModule(ModuleManager.ModuleType.ConstructionModule.getName());
                    consModule.getProvidedItems().forEach((itemId, count) -> {
                        totalReturnItems.merge(itemId, count, Integer::sum);
                    });
                }
            });

            var btnItem = new ItemStack(Material.STONE);
            var im = btnItem.getItemMeta();
            im.setDisplayName("§c§l拆除建筑(投票)");
            if (unableToDestroyList.contains(landTypeName)) {
                im.setDisplayName("§c§l拆除建筑(管理员模式)");
            }
            var lore = new ArrayList<Component>();
            if (!totalReturnItems.isEmpty()) {
                lore.add(Component.text("拆除后可获得:").color(NamedTextColor.WHITE));
                totalReturnItems.forEach((itemId, count) -> {
                    var itemName = ItemManager.getItem(itemId).displayName();
                    lore.add(Component.text("  - ").append(itemName).append(Component.text(" x" + count))
                            .color(NamedTextColor.WHITE));
                });
            }
            im.lore(lore);
            btnItem.setItemMeta(im);
            occupiedBuildMenu.getSlot(slotIndex[0]).setItem(btnItem);
            occupiedBuildMenu.getSlot(slotIndex[0]).setClickHandler((player, info) -> {
                // 关闭菜单
                occupiedBuildMenu.close(player);

                int voteTimeout = FrostCraftBU.i().getConfig().getInt("voteTimeout", 60);
                VoteSession session = VoteManager.i().createVote(player, "拆除投票",
                        player.getName() + "发起了拆除建筑:" + building.getBuildingId() + "的投票，请在" + voteTimeout + "秒内投出你的意见。",
                        voteTimeout);
                // player.sendMessage("§a投票已创建！ID: " + session.getId());
                session.getFuture().thenApply(result -> {
                    if (result == VoteSession.VoteResult.APPROVED) {
                        // 公告
                        Bukkit.broadcast(Component.text("§a投票通过！拆除建筑工地: " + building.getBuildingId())
                                .color(NamedTextColor.GREEN));
                        BuildingManager.i().removeBuildingAt(signLoc, true);
                        // 掉落材料
                        totalReturnItems.forEach((itemId, count) -> {
                            var item = ItemManager.getItem(itemId);
                            item.setAmount(count);
                            player.getWorld().dropItemNaturally(player.getLocation(), item);
                        });
                    } else if (result == VoteSession.VoteResult.REJECTED) {
                        Bukkit.broadcast(Component.text("§c投票未通过！取消拆除建筑工地: " + building.getBuildingId())
                                .color(NamedTextColor.RED));
                    } else if (result == VoteSession.VoteResult.TIMEOUT) {
                        Bukkit.broadcast(Component.text("§7投票超时！取消拆除建筑工地: " + building.getBuildingId())
                                .color(NamedTextColor.GRAY));
                    } else if (result == VoteSession.VoteResult.CANCELLED) {
                        Bukkit.broadcast(Component.text("§7投票被取消！取消拆除建筑工地: " + building.getBuildingId())
                                .color(NamedTextColor.GRAY));
                    }
                    return null;
                });
                session.vote(player, true);
                // // 更新牌子内容
                // signState.getSide(Side.FRONT).setLine(0,
                // FrostCraftBU.i().getConfig().getString("EmptyLandSign0"));
                // signState.getSide(Side.FRONT).setLine(1,
                // FrostCraftBU.i().getConfig().getString("EmptyLandSign1"));
                // signState.getSide(Side.FRONT).setLine(2, (String) landTypeName);
                // signState.getSide(Side.FRONT).setLine(3, "");
                // signState.update();
                // // 播放拆除音效
                // SoundEffectHelper.PlaySoundAtPlayer(player, CUSTOM_SOUND.DESTROY);

            });
            slotIndex[0]++;
        }
        // 在之后添加该建筑的所有IChestUI按钮
        var buildingModules = building.getModules();
        buildingModules.forEach((moduleId, module) -> {
            if (module instanceof IChestUI) {
                var icon = ((IChestUI) module).getIcon();
                occupiedBuildMenu.getSlot(slotIndex[0]).setItem(icon);
                occupiedBuildMenu.getSlot(slotIndex[0]).setClickOptions(
                        ClickOptions.builder().allow(ClickType.LEFT).allow(InventoryAction.PICKUP_ONE).build());
                occupiedBuildMenu.getSlot(slotIndex[0]).setClickHandler((player, info) -> {
                    occupiedBuildMenu.close(player);
                    ((IChestUI) module).getUI().open(player);
                });
                slotIndex[0]++;
            }
        });
        occupiedBuildMenu.open(p);
    }

}
