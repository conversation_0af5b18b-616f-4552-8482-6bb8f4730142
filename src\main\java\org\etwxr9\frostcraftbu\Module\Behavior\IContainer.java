package org.etwxr9.frostcraftbu.Module.Behavior;

import java.util.List;

public interface IContainer {

    int getSlotCount();

    String getSlotItemId(int slotIndex);

    int getSlotItemCount(int slotIndex);

    List<String> getSlotTags(int slotIndex);

    List<Integer> getSlotsByTag(String tag);

    boolean hasSlotTag(int slotIndex, String tag);

    void setSlotItem(int slotIndex, String itemId, int count);

    void cosumeItem(int slotIndex, int count);

    void produceItem(int slotIndex, int count);

    int getSlotMaxCount(int slotIndex);

    void clearSlot(int slotIndex);

}
