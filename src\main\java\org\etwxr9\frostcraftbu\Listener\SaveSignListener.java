package org.etwxr9.frostcraftbu.Listener;

import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.block.Sign;
import org.bukkit.block.sign.Side;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.event.inventory.InventoryAction;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.persistence.PersistentDataType;
import org.bukkit.util.Vector;
import org.etwxr9.buildingunit.BuildingUnitAPI;
import org.etwxr9.frostcraftbu.FrostCraftBU;
import org.etwxr9.frostcraftbu.Building.BuildingManager;
import org.ipvp.canvas.slot.ClickOptions;
import org.ipvp.canvas.type.ChestMenu;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

public class SaveSignListener implements Listener {

    @EventHandler
    public void OnRClickSign(PlayerInteractEvent e) {
        if (e.getAction() != Action.RIGHT_CLICK_BLOCK)
            return;
        var p = e.getPlayer();
        if (!p.isOp())
            return;
        var signBlock = e.getClickedBlock();
        if (!(signBlock.getState() instanceof Sign))
            return;
        var signData = ((Sign) signBlock.getState()).getSide(Side.FRONT).getLines();
        // 检查是否是保存建筑的牌子
        if (!signData[0].equals(FrostCraftBU.i().getConfig().getString("SaveBuildingSign0")))
            return;
        e.setCancelled(true);
        // 检查建筑名
        var buildingName = signData[1];
        if (buildingName == null || buildingName.equals(""))
            return;
        var loc1 = e.getClickedBlock().getLocation().clone().add(new Vector(1, 0, 1));
        var loc2 = e.getClickedBlock().getLocation().clone()
                .add(BuildingManager.i().getBuildingConfig(buildingName).getSize());
        // 打开菜单
        var emptyBuildMenu = ChestMenu.builder(1)
                .title("管理模板")
                .build();
        // 粒子提示按钮
        emptyBuildMenu.getSlot(0).setClickOptions(
                ClickOptions.builder().allow(ClickType.LEFT).allow(InventoryAction.PICKUP_ONE).build());
        var btnParticleItem = new ItemStack(Material.LANTERN);
        var particleIm = btnParticleItem.getItemMeta();
        particleIm.setDisplayName("显示边框粒子提示");
        btnParticleItem.setItemMeta(particleIm);
        emptyBuildMenu.getSlot(0).setItem(btnParticleItem);
        emptyBuildMenu.getSlot(0).setClickHandler((player, info) -> {
            BuildingUnitAPI.showCubeParticle(loc1, loc2, List.of(p), 255, 0, 0, 20, 10);
            p.sendMessage(FrostCraftBU.i().getConfig().getString("ShowParticleInfo"));
            emptyBuildMenu.close();
        });
        // 建筑信息
        emptyBuildMenu.getSlot(1).setClickOptions(
                ClickOptions.builder().allow(ClickType.LEFT).allow(InventoryAction.PICKUP_ONE).build());
        var btnBuildingConfig = new ItemStack(Material.BOOK);
        var configIm = btnBuildingConfig.getItemMeta();
        configIm.setDisplayName("显示建筑信息");
        btnBuildingConfig.setItemMeta(configIm);
        emptyBuildMenu.getSlot(1).setItem(btnBuildingConfig);
        emptyBuildMenu.getSlot(1).setClickHandler((player, config) -> {
            var conf = BuildingManager.i().getBuildingConfig(buildingName);
            p.sendMessage("机器名称:" + conf.getId());
            // FrostCraftBU.getInstance().getLogger().info(mI.getBehavior().toString());
            if (conf.getAllModuleConfig().containsKey("container")) {
                p.sendMessage("容器属性:");
                var container = conf.getAllModuleConfig().get("container");
                var containerMsg = "物品接口: 输入：" + container.getInt("input") + " 输出：" + container.getInt("output");
                p.sendMessage(containerMsg);
            }
            emptyBuildMenu.close();
        });
        // 保存按钮
        emptyBuildMenu.getSlot(2).setClickOptions(
                ClickOptions.builder().allow(ClickType.LEFT).allow(InventoryAction.PICKUP_ONE).build());
        var btnSaveItem = new ItemStack(Material.EMERALD);
        var saveIm = btnSaveItem.getItemMeta();
        saveIm.setDisplayName("保存建筑");
        btnSaveItem.setItemMeta(saveIm);
        emptyBuildMenu.getSlot(2).setItem(btnSaveItem);
        emptyBuildMenu.getSlot(2).setClickHandler((player, info) -> {
            // get all itemFrame and check container slot
            // var pdcKey = ContainerBehaivor.getItemNodeKey();
            // var mI = MachineManager.i().getMachineInfo(buildingName);
            // if (mI.getBehavior().containsKey("container")) {
            //     var configInputAmount = mI.getBehavior().get("container").getInt("input");
            //     var configOutputAmount = mI.getBehavior().get("container").getInt("output");
            //     ArrayList<Integer> configIndexes = new ArrayList<>();
            //     for (int i = 0; i < configInputAmount; i++) {
            //         configIndexes.add(i + 1);
            //     }
            //     for (int i = 0; i < configOutputAmount; i++) {
            //         configIndexes.add(-i - 1);
            //     }
            //     var r = loc2.clone().subtract(loc1).multiply(0.5);
            //     var center = loc1.clone().add(loc2).multiply(0.5);
            //     var frames = loc1.getWorld().getNearbyEntities(center, r.getX(), r.getY(), r.getZ(),
            //             entity -> entity.getType().equals(org.bukkit.entity.EntityType.ITEM_FRAME));
            //     ArrayList<Integer> saveIndexes = new ArrayList<>();
            //     frames.forEach(f -> {
            //         var index = f.getPersistentDataContainer().get(pdcKey, PersistentDataType.INTEGER);
            //         if (index == null) {
            //             return;
            //         }
            //         saveIndexes.add(index);
            //     });
            //     Collections.sort(saveIndexes);
            //     Collections.sort(configIndexes);
            //     if (!saveIndexes.equals(configIndexes)) {
            //         p.sendMessage("容器配置不匹配！");
            //         p.sendMessage("配置：" + configIndexes.toString());
            //         p.sendMessage("实际：" + saveIndexes.toString());
            //         return;
            //     }
            // }
            BuildingUnitAPI.saveSchematic(loc1, loc2, buildingName, loc1);
            p.sendMessage(
                    FrostCraftBU.i().getConfig().getString("SaveBuildingInfo").replace("{name}", buildingName));
            emptyBuildMenu.close();
        });
        emptyBuildMenu.open(p);
    }

}
