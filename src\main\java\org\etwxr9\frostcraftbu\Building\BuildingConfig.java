package org.etwxr9.frostcraftbu.Building;

import org.bukkit.Material;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.inventory.ItemStack;
import org.bukkit.util.Vector;
import org.etwxr9.frostcraftbu.FrostCraftBU;

import java.util.ArrayList;
import java.util.HashMap;

import javax.print.DocFlavor.STRING;

/**
 * 用于记录Building.yml中的机器配置，包括名称，图标，类型，消耗，输入节点，输出节点，其他属性可以从buildingSection中获取
 */
public class BuildingConfig {

    // 属性
    private String id;
    private String name;
    private String iconItemId;
    private HashMap<String, Integer> consume;
    private HashMap<String, ConfigurationSection> module;
    private Vector size; // 可能在构造时为 null
    private String consSiteType; // 用于延迟查找 size

    public Vector getSize() {
        // 如果 size 未初始化且存在 landType，则尝试从 landType 获取 size
        if (this.size == null && this.consSiteType != null) {
            BuildingConfig landConfig = BuildingManager.i().getBuildingConfig(this.consSiteType);
            if (landConfig != null) {
                // 获取 landType 的 size 并缓存，注意避免无限递归（如果 landType 也依赖其他类型）
                // 假设 landType 不会形成复杂的依赖链导致此调用也失败
                this.size = landConfig.getSize();
            } else {
                FrostCraftBU.i().getLogger().warning("Could not find landType '" + this.consSiteType + "' for building '" + this.id + "' to determine size.");
                // 提供一个默认值或标记为错误状态
                this.size = new Vector(FrostCraftBU.i().getConfig().getInt("buildingDefaultSizeX", 1),
                                       FrostCraftBU.i().getConfig().getInt("buildingDefaultSizeY", 1),
                                       FrostCraftBU.i().getConfig().getInt("buildingDefaultSizeZ", 1));
            }
        } else if (this.size == null) {
             // 如果没有 landType 且 size 仍然为 null，则使用默认值
             this.size = new Vector(FrostCraftBU.i().getConfig().getInt("buildingDefaultSizeX", 1),
                                    FrostCraftBU.i().getConfig().getInt("buildingDefaultSizeY", 1),
                                    FrostCraftBU.i().getConfig().getInt("buildingDefaultSizeZ", 1));
        }
        return size;
    }

    public BuildingConfig(ConfigurationSection buildingSection) {
        this.id = buildingSection.getName();
        this.name = buildingSection.getString("name");
        this.consSiteType = buildingSection.getString("consSiteType"); // 先保存 landType

        // 尝试读取 size
        var sizeList = buildingSection.getIntegerList("size");
        if (sizeList != null && sizeList.size() >= 3) {
            this.size = new Vector(sizeList.get(0), sizeList.get(1), sizeList.get(2));
        } else {
            // 不在此处立即解析 landType，getSize() 会处理
            this.size = null;
        }

        // 读取consume
        this.consume = new HashMap<>();
        var consumeSection = buildingSection.getConfigurationSection("consume");
        if (consumeSection != null) {
            for (var key : consumeSection.getKeys(false)) {
                this.consume.put(key, consumeSection.getInt(key));
            }
        }

        // 读取module
        this.module = new HashMap<>();
        var moduleSection = buildingSection.getConfigurationSection("module");
        if (moduleSection != null) {
            for (var key : moduleSection.getKeys(false)) {
                this.module.put(key.toLowerCase(), moduleSection.getConfigurationSection(key));
            }
        }

        // 读取ICON
        this.iconItemId = buildingSection.getString("icon");
    }

    public String getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getIconItemId() {
        return iconItemId;
    }

    public HashMap<String, Integer> getConsume() {
        return consume;
    }

    public HashMap<String, ConfigurationSection> getAllModuleConfig() {
        return module;
    }

    public ConfigurationSection getModuleConfig(String moduleId) {
        return module.get(moduleId.toLowerCase());
    }

    public String getConsSiteType() {
        return consSiteType;
    }
}
