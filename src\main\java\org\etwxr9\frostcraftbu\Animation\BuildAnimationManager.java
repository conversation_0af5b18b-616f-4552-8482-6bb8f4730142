package org.etwxr9.frostcraftbu.Animation;

import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.entity.Entity;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.util.Vector;
import org.etwxr9.frostcraftbu.FrostCraftBU;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Random;
import java.util.concurrent.CompletableFuture;

public class BuildAnimationManager {
    private static BuildAnimationManager instance;
    private final HashMap<Entity, List<Location>> buildDisplayPathMap = new HashMap<>();
    private final HashMap<Location, Location> particleMap = new HashMap<>();

    private final static int BUILDING_DISPLAY_INTERVAL = 2;
    private final static int BUILDING_DISPLAY_PATH_LENGTH = 10;

    private BuildAnimationManager() {
        startPathAnimation();
        startParticleAnimation();
    }

    public static BuildAnimationManager getInstance() {
        if (instance == null) {
            instance = new BuildAnimationManager();
        }
        return instance;
    }

    private void startPathAnimation() {
        new BukkitRunnable() {
            @Override
            public void run() {
                buildDisplayPathMap.entrySet().removeIf(entry -> {
                    var k = entry.getKey();
                    var v = entry.getValue();
                    if (v.size() == 0) {
                        k.remove();
                        return true;
                    }
                    var loc = v.get(0);
                    k.teleport(loc);
                    v.remove(0);
                    return false;
                });
            }
        }.runTaskTimer(FrostCraftBU.i(), 0, BUILDING_DISPLAY_INTERVAL);
    }

    private void startParticleAnimation() {
        new BukkitRunnable() {
            @Override
            public void run() {
                Random random = new Random();
                particleMap.forEach((signLoc, centerLoc) -> {
                    for (int i = 0; i < 3; i++) {
                        double yDir = random.nextDouble() * 0.4;
                        double angle = 2 * Math.PI * random.nextDouble();
                        double xDir = Math.cos(angle);
                        double zDir = Math.sin(angle);
                        double speed = random.nextDouble() * 0.3 + 0.1;
                        Location particleLoc = centerLoc.clone().add(xDir, yDir, zDir);
                        centerLoc.getWorld().spawnParticle(Particle.POOF, particleLoc, 0, xDir, yDir, zDir, speed);
                    }
                });
            }
        }.runTaskTimer(FrostCraftBU.i(), 0, 1);
    }

    public CompletableFuture<Void> playParticleAnimation(Location signLocation, Location centerLocation) {
        CompletableFuture<Void> future = new CompletableFuture<>();
        particleMap.put(signLocation, centerLocation);

        new BukkitRunnable() {
            @Override
            public void run() {
                particleMap.remove(signLocation);
                future.complete(null);
            }
        }.runTaskLater(FrostCraftBU.i(), BUILDING_DISPLAY_INTERVAL * BUILDING_DISPLAY_PATH_LENGTH);

        return future;
    }

    public CompletableFuture<Void> playPathAnimation(Entity entity, Location start, Location end) {
        CompletableFuture<Void> future = new CompletableFuture<>();
        buildDisplayPathMap.put(entity, BeamPathGenerator.genPath(start, end));

        new BukkitRunnable() {
            @Override
            public void run() {
                buildDisplayPathMap.remove(entity);
                future.complete(null);
            }
        }.runTaskLater(FrostCraftBU.i(), BUILDING_DISPLAY_INTERVAL * BUILDING_DISPLAY_PATH_LENGTH);

        return future;
    }

    public CompletableFuture<Void> playMultiPathAnimation(List<Entity> entities, Location start, Location end, int delayBetweenEntities) {
        CompletableFuture<Void> groupFuture = new CompletableFuture<>();
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        
        // 为每个实体创建延迟任务
        for (int i = 0; i < entities.size(); i++) {
            Entity entity = entities.get(i);
            int delay = i * delayBetweenEntities;
            
            CompletableFuture<Void> entityFuture = new CompletableFuture<>();
            futures.add(entityFuture);
            
            // 延迟创建每个实体的动画
            new BukkitRunnable() {
                @Override
                public void run() {
                    buildDisplayPathMap.put(entity, BeamPathGenerator.genPath(start, end));
                    
                    // 设置动画结束时的回调
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            buildDisplayPathMap.remove(entity);
                            entityFuture.complete(null);
                        }
                    }.runTaskLater(FrostCraftBU.i(), BUILDING_DISPLAY_INTERVAL * BUILDING_DISPLAY_PATH_LENGTH);
                }
            }.runTaskLater(FrostCraftBU.i(), delay);
        }
        
        // 当所有实体的动画都完成时，完成组Future
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
            .thenRun(() -> groupFuture.complete(null));
        
        return groupFuture;
    }

    private class BeamPathGenerator {
        private static final Random random = new Random();

        private static List<Vector> generateBeamPath(Vector start, Vector end, int numPoints) {
            List<Vector> path = new ArrayList<>();
            Vector direction = end.clone().subtract(start);
            double length = Math.sqrt(direction.getX() * direction.getX() + direction.getY() * direction.getY()
                    + direction.getZ() * direction.getZ());

            // 生成控制点
            Vector control1 = generateControlPoint(start, direction, length, 0.3, 0.5);
            Vector control2 = generateControlPoint(start, direction, length, 0.5, 0.7);

            // 使用贝塞尔曲线生成路径点
            for (int i = 0; i < numPoints; i++) {
                double t = i / (double) (numPoints - 1);
                Vector point = cubicBezier(start, control1, control2, end, t);
                path.add(point);
            }

            return path;
        }

        private static Vector generateControlPoint(Vector start, Vector direction, double length, double minOffset,
                double maxOffset) {
            double offset = random.nextDouble() * (maxOffset - minOffset) + minOffset;
            double angle = random.nextDouble() * Math.PI * 2;
            Vector perpendicular = generatePerpendicularVector(direction, angle);
            double strength = random.nextDouble() * length * 0.5;
            return start.clone().add(direction.clone().multiply(offset)).add(perpendicular.clone().multiply(strength));
        }

        private static Vector generatePerpendicularVector(Vector v, double angle) {
            Vector temp = Math.abs(v.getX()) < Math.abs(v.getY()) ? new Vector(1, 0, 0) : new Vector(0, 1, 0);

            Vector perpendicular = new Vector(
                    v.getY() * temp.getZ() - v.getZ() * temp.getY(),
                    v.getZ() * temp.getX() - v.getX() * temp.getZ(),
                    v.getX() * temp.getY() - v.getY() * temp.getX());
            double magnitude = Math.sqrt(perpendicular.getX() * perpendicular.getX()
                    + perpendicular.getY() * perpendicular.getY() + perpendicular.getZ() * perpendicular.getZ());

            perpendicular.multiply(1 / magnitude);
            // 计算第二个垂直向量（与第一个和原向量都垂直）
            Vector perpendicular2 = new Vector(
                    v.getY() * perpendicular.getZ() - v.getZ() * perpendicular.getY(),
                    v.getZ() * perpendicular.getX() - v.getX() * perpendicular.getZ(),
                    v.getX() * perpendicular.getY() - v.getY() * perpendicular.getX());
            perpendicular2.multiply(1 / magnitude);
            return perpendicular.multiply(Math.cos(angle)).add(perpendicular2.multiply(Math.sin(angle)));
            // return perpendicular.multiply(1 / magnitude);
        }

        private static Vector cubicBezier(Vector p0, Vector p1, Vector p2, Vector p3, double t) {
            double u = 1 - t;
            double tt = t * t;
            double uu = u * u;
            double uuu = uu * u;
            double ttt = tt * t;

            Vector point = p0.clone().multiply(uuu);
            point.add(p1.clone().multiply(3 * uu * t));
            point.add(p2.clone().multiply(3 * u * tt));
            point.add(p3.clone().multiply(ttt));

            // log
            // FrostCraftBU.i().getLogger().info("point:" + point.toString());

            return point;
        }

        public static List<Location> genPath(Location startLoc, Location endLoc) {
            Vector start = startLoc.toVector();
            Vector end = endLoc.toVector();
            var vecL = generateBeamPath(start, end, BUILDING_DISPLAY_PATH_LENGTH);
            List<Location> locL = new ArrayList<>();
            var yaw = random.nextFloat() * 360;
            var pitch = random.nextFloat() * 360;
            vecL.forEach(v -> {
                locL.add(v.toLocation(startLoc.getWorld(), yaw, pitch));
            });
            return locL;
        }
    }
}
