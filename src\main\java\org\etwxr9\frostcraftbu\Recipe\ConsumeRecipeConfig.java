package org.etwxr9.frostcraftbu.Recipe;

import java.util.List;
import java.util.Map;

public class ConsumeRecipeConfig {
    private String id;
    private String name;
    private Map<String, Integer> input;
    private int time;
    private int scoreboardValue;

    // getters and setters
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    public Map<String, Integer> getInput() { return input; }
    public void setInput(Map<String, Integer> input) { this.input = input; }
    public int getTime() { return time; }
    public void setTime(int time) { this.time = time; }
    public int getScoreboardValue() { return scoreboardValue; }
    public void setScoreboardValue(int scoreboardValue) { this.scoreboardValue = scoreboardValue; }

}
