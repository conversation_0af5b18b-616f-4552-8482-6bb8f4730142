package org.etwxr9.frostcraftbu.Item;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

import org.bukkit.Material;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.event.inventory.InventoryAction;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.etwxr9.frostcraftbu.FrostCraftBU;
import org.etwxr9.frostcraftbu.Tech.TechTreeManager;
import org.ipvp.canvas.mask.BinaryMask;
import org.ipvp.canvas.mask.Mask;
import org.ipvp.canvas.paginate.PaginatedMenuBuilder;
import org.ipvp.canvas.slot.ClickOptions;
import org.ipvp.canvas.slot.SlotSettings;
import org.ipvp.canvas.type.ChestMenu;

import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextDecoration;

public class ItemManager {

    private static HashMap<String, ItemStack> registeredItems = new HashMap<>();

    public static void registerItem(String id, ItemStack item) {
        registeredItems.put(id, item);
        saveItems();
    }

    public static void unregisterItem(String id) {
        registeredItems.remove(id);
        saveItems();
    }

    public static ItemStack getItem(String id) {
        ItemStack result;
        if (registeredItems.containsKey(id)) {
            result = registeredItems.get(id).clone();
        } else {
            result = new ItemStack(Material.BARRIER);
            var itemMeta = result.getItemMeta();
            itemMeta.displayName(Component.text("物品未找到！(" + id + ")").color(NamedTextColor.RED));
            itemMeta.lore(List.of(Component.text("物品未找到！(" + id + ")").color(NamedTextColor.RED)));
            var pdc = itemMeta.getPersistentDataContainer();
            pdc.set(new org.bukkit.NamespacedKey(FrostCraftBU.i(), "itemNotFound"),
                    org.bukkit.persistence.PersistentDataType.STRING, id);
            result.setItemMeta(itemMeta);
        }
        return result;
    }

    public static String getItemId(ItemStack item) {
        for (String id : registeredItems.keySet()) {
            if (registeredItems.get(id).isSimilar(item)) {
                return id;
            }
        }
        return null;
    }

    public static HashMap<String, ItemStack> getRegisteredItems() {
        return registeredItems;
    }

    public static void playerRegisterItem(Player p, String id, ItemStack item) {
        // if (registeredItems.containsKey(id)) {
        // p.sendMessage("§c[ItemManager]Item already registered");
        // return;
        // }
        registerItem(id, item);
        p.sendMessage("§a[ItemManager]Item registered as " + id);
    }

    public static void playerUnregisterItem(Player p, String id) {
        if (!registeredItems.containsKey(id)) {
            p.sendMessage("§c[ItemManager]Item not registered");
            return;
        }
        unregisterItem(id);
        p.sendMessage("§a[ItemManager]Item unregistered as " + id);
    }

    public static void playerListItems(Player p) {
        p.sendMessage("§a[ItemManager]Registered items:");
        for (String id : registeredItems.keySet()) {
            p.sendMessage(id);
        }
    }

    public static void openItemUI(Player p) {
        // 定义物品放置的槽位掩码（1-5行）
        Mask itemMask = BinaryMask.builder(ChestMenu.builder(6).build())
                .pattern("111111111")
                .pattern("111111111")
                .pattern("111111111")
                .pattern("111111111")
                .pattern("111111111")
                .pattern("000000000") // 最后一行保留给控制按钮
                .build();

        // 创建分页菜单
        PaginatedMenuBuilder builder = PaginatedMenuBuilder.builder(ChestMenu.builder(6).title("物品图鉴").redraw(true))
                .slots(itemMask)
                .previousButton(createNamedItem(Material.ARROW, "上一页"))
                .previousButtonSlot(48)
                .nextButton(createNamedItem(Material.ARROW, "下一页"))
                .nextButtonSlot(50);

        // 添加物品
        for (String itemId : registeredItems.keySet()) {
            var itemIcon = getItem(itemId);
            itemIcon.editMeta(meta -> {
                var lore = meta.lore() == null ? new ArrayList<Component>() : meta.lore();
                lore.addFirst(Component.text("id: " + itemId).color(NamedTextColor.GREEN));
                meta.lore(lore);
            });
            builder.addItem(SlotSettings.builder()
                    .item(itemIcon)
                    .clickOptions(ClickOptions.builder().allow(ClickType.LEFT).allow(InventoryAction.PICKUP_ONE).build())
                    .clickHandler((player, info) -> {
                        player.getInventory().addItem(getItem(itemId));
                        player.sendMessage(Component.text("已将物品放入背包").color(NamedTextColor.GREEN));
                    })
                    .build());
        }
        builder.build().get(0).open(p);
    }

    private static ItemStack createNamedItem(Material material, String name) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        if (meta != null) {
            meta.displayName(Component.text(name).decoration(TextDecoration.ITALIC, false));
            item.setItemMeta(meta);
        }
        return item;
    }

    public static void saveItems() {
        File file = new File(FrostCraftBU.i().getDataFolder().getAbsolutePath() + "/registeredItems.yml");
        YamlConfiguration config = YamlConfiguration.loadConfiguration(file);
        for (String id : registeredItems.keySet()) {
            config.set(id, itemToString(registeredItems.get(id)));
        }
        try {
            config.save(file);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void loadItems() {
        File file = new File(FrostCraftBU.i().getDataFolder().getAbsolutePath() + "/registeredItems.yml");
        YamlConfiguration config = YamlConfiguration.loadConfiguration(file);
        for (String id : config.getKeys(false)) {
            registeredItems.put(id, stringToItem(config.getString(id)));
        }
    }

    public static String itemToString(ItemStack item) {
        byte[] bytes = item.serializeAsBytes();
        return java.util.Base64.getEncoder().encodeToString(bytes);
    }

    public static ItemStack stringToItem(String str) {
        byte[] bytes = java.util.Base64.getDecoder().decode(str);
        return ItemStack.deserializeBytes(bytes);
    }

    public static String itemArrayToString(ItemStack[] items) {
        byte[] bytes = ItemStack.serializeItemsAsBytes(items);
        return java.util.Base64.getEncoder().encodeToString(bytes);
    }

    public static ItemStack[] stringToItemArray(String str) {
        byte[] bytes = java.util.Base64.getDecoder().decode(str);
        return ItemStack.deserializeItemsFromBytes(bytes);
    }
}
