package org.etwxr9.frostcraftbu.Recipe;

import java.util.HashMap;
import java.util.Map;

public class RecipeConfig {
    private String id;
    private String name;
    private Map<String, Integer> input;
    private Map<String, Integer> output;
    private int time;
    private int steam;

    // getters and setters
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    public Map<String, Integer> getInput() { return input; }
    public void setInput(Map<String, Integer> input) { this.input = input; }
    public Map<String, Integer> getOutput() { return output; }
    public void setOutput(Map<String, Integer> output) { this.output = output; }
    public int getTime() { return time; }
    public void setTime(int time) { this.time = time; }
    public int getSteam() { return steam; }
    public void setSteam(int steam) { this.steam = steam; }
}