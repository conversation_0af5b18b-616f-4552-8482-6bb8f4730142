package org.etwxr9.frostcraftbu.pathfinding.api;

import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.etwxr9.frostcraftbu.FrostCraftBU;

import java.util.List;

/**
 * 无人机寻路API使用示例
 * 
 * 展示如何在其他模块中使用DronePathfindingService
 */
public class DronePathfindingExample {
    
    /**
     * 示例1：简单的异步寻路
     */
    public void simplePathfindingExample(Player player, Location start, Location end) {
        DronePathfindingService service = FrostCraftBU.getDronePathfindingService();
        
        if (service == null) {
            player.sendMessage("§c无人机寻路服务未初始化！");
            return;
        }
        
        // 执行简单的异步寻路
        service.findPathAsync(start, end)
            .thenAccept(result -> {
                if (result.isSuccessful()) {
                    player.sendMessage("§a寻路成功！路径长度: " + result.getPathSize() + " 个节点");
                    
                    // 获取路径点
                    List<Location> path = result.getRawPath();
                    player.sendMessage("§7起点: " + formatLocation(path.get(0)));
                    player.sendMessage("§7终点: " + formatLocation(path.get(path.size() - 1)));
                } else {
                    player.sendMessage("§c寻路失败: " + result.getErrorMessage());
                }
            })
            .exceptionally(throwable -> {
                player.sendMessage("§c寻路异常: " + throwable.getMessage());
                return null;
            });
    }
    
    /**
     * 示例2：带平滑的高级寻路
     */
    public void advancedPathfindingExample(Player player, Location start, Location end) {
        DronePathfindingService service = FrostCraftBU.getDronePathfindingService();
        
        if (service == null) {
            player.sendMessage("§c无人机寻路服务未初始化！");
            return;
        }
        
        // 基本寻路
        service.findPathAsync(start, end)
            .thenAccept(result -> {
                if (result.isSuccessful()) {
                    player.sendMessage("§a寻路成功！");
                    player.sendMessage("§7原始路径: " + result.getRawPath().size() + " 个节点");

                    player.sendMessage("§7路径总长度: " + String.format("%.2f", result.getPathLength()) + " 格");
                    player.sendMessage("§7计算耗时: " + result.getComputeTime() + "ms");

                    if (result.hasFallback()) {
                        player.sendMessage("§e注意: 使用了备用寻路策略");
                    }
                } else {
                    player.sendMessage("§c寻路失败: " + result.getErrorMessage());
                }
            });
    }
    
    /**
     * 示例3：同步寻路（在异步上下文中使用）
     */
    public void synchronousPathfindingExample(Player player, Location start, Location end) {
        // 注意：这应该在异步任务中调用，避免阻塞主线程
        FrostCraftBU.i().getServer().getScheduler().runTaskAsynchronously(FrostCraftBU.i(), () -> {
            DronePathfindingService service = FrostCraftBU.getDronePathfindingService();
            
            if (service == null) {
                player.sendMessage("§c无人机寻路服务未初始化！");
                return;
            }
            
            // 执行同步寻路
            DronePathResult result = service.findPathSync(start, end);
            
            // 回到主线程更新UI
            FrostCraftBU.i().getServer().getScheduler().runTask(FrostCraftBU.i(), () -> {
                if (result.isSuccessful()) {
                    player.sendMessage("§a同步寻路完成！路径长度: " + result.getPathSize());
                    
                    // 处理路径
                    List<Location> path = result.getRawPath();
                    highlightPath(path, player);
                } else {
                    player.sendMessage("§c同步寻路失败: " + result.getErrorMessage());
                }
            });
        });
    }
    
    /**
     * 示例4：批量寻路
     */
    public void batchPathfindingExample(Player player, List<Location> waypoints) {
        DronePathfindingService service = FrostCraftBU.getDronePathfindingService();
        
        if (service == null || waypoints.size() < 2) {
            player.sendMessage("§c无效的寻路请求！");
            return;
        }
        
        player.sendMessage("§6开始批量寻路，共 " + (waypoints.size() - 1) + " 段路径...");
        
        // 递归处理每一段路径
        processBatchPathfinding(service, player, waypoints, 0, 0.0);
    }
    
    /**
     * 递归处理批量寻路
     */
    private void processBatchPathfinding(DronePathfindingService service, Player player, 
                                       List<Location> waypoints, int index, double totalDistance) {
        if (index >= waypoints.size() - 1) {
            player.sendMessage("§a批量寻路完成！总距离: " + String.format("%.2f", totalDistance) + " 格");
            return;
        }
        
        Location start = waypoints.get(index);
        Location end = waypoints.get(index + 1);
        
        service.findPathAsync(start, end)
            .thenAccept(result -> {
                if (result.isSuccessful()) {
                    double segmentDistance = result.getPathLength();
                    player.sendMessage("§7段 " + (index + 1) + ": " + String.format("%.2f", segmentDistance) + " 格");
                    
                    // 继续处理下一段
                    processBatchPathfinding(service, player, waypoints, index + 1, totalDistance + segmentDistance);
                } else {
                    player.sendMessage("§c段 " + (index + 1) + " 寻路失败: " + result.getErrorMessage());
                }
            });
    }
    

    
    /**
     * 高亮显示路径的示例方法
     */
    private void highlightPath(List<Location> path, Player player) {
        // 示例：使用方块变化来显示路径
        for (Location loc : path) {
            // 临时显示路径点
            player.sendBlockChange(loc, org.bukkit.Material.GLOWSTONE.createBlockData());
        }
        
        // 5秒后恢复原始方块
        FrostCraftBU.i().getServer().getScheduler().runTaskLater(FrostCraftBU.i(), () -> {
            for (Location loc : path) {
                // 恢复原始方块
                player.sendBlockChange(loc, loc.getBlock().getBlockData());
            }
        }, 100L); // 5秒 = 100 ticks
    }
    
    /**
     * 格式化位置信息
     */
    private String formatLocation(Location location) {
        return String.format("(%.1f, %.1f, %.1f)", 
            location.getX(), location.getY(), location.getZ());
    }
}
