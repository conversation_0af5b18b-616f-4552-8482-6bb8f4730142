package org.etwxr9.frostcraftbu.Building;

import static com.fasterxml.jackson.annotation.JsonTypeInfo.Id.NAME;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.bukkit.Location;
import org.etwxr9.buildingunit.BuildingUnitAPI;
import org.etwxr9.frostcraftbu.FrostCraftBU;
import org.etwxr9.frostcraftbu.Module.BaseModule;
import org.etwxr9.frostcraftbu.Module.BaseModuleSaveData;
import org.etwxr9.frostcraftbu.Module.ModuleManager;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonTypeInfo;

public class FCBuilding implements IPersistableState<org.etwxr9.frostcraftbu.Building.FCBuilding.FCBuildingSaveData> {

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FCBuildingSaveData {
        private String machineId;
        private String unitUUID;
        private ArrayList<BaseModuleSaveData> moduleSaveDatas;

        public String getMachineId() {
            return machineId;
        }

        public String getUnitUUID() {
            return unitUUID;
        }

        public ArrayList<BaseModuleSaveData> getModuleSaveDatas() {
            return moduleSaveDatas;
        }

    }

    @Override
    public FCBuildingSaveData getSaveData() {
        FCBuildingSaveData saveData = new FCBuildingSaveData();
        saveData.machineId = machineId;
        saveData.unitUUID = unitUUID;
        saveData.moduleSaveDatas = new ArrayList<>();
        for (var module : modules.values()) {
            saveData.moduleSaveDatas.add(module.getSaveData());
        }
        return saveData;
    }

    @Override
    public void loadSaveData(FCBuildingSaveData data) {
        machineId = data.machineId;
        unitUUID = data.unitUUID;
        for (var moduleSaveData : data.moduleSaveDatas) {
            // log
            // FrostCraftBU.i().getLogger().info("load module: " + moduleSaveData.getModuleTypeId());
            var module = modules.get(moduleSaveData.getModuleTypeId());
            module.loadSaveData(moduleSaveData);
        }
    }

    FCBuilding() {
    }

    private String machineId;
    private String unitUUID;
    // 升级前的建筑记录，用于拆卸时计算返还材料
    private List<String> originBuildingIds;
    private HashMap<String, BaseModule> modules;

    public void addModule(String id, BaseModule module) {
        if (modules.containsKey(id)) {
            throw new IllegalArgumentException("Module already exists");
        }
        modules.put(id, module);
    }

    public BaseModule getModule(String id) {
        return modules.get(id);
    }

    public HashMap<String, BaseModule> getModules() {
        return modules;
    }

    public FCBuilding(String machineId, String unitUUID) {
        this.machineId = machineId;
        this.unitUUID = unitUUID;
        modules = new HashMap<>();
        originBuildingIds = new ArrayList<>();
    }

    // 机器ID
    public String getBuildingId() {
        return machineId;
    }

    public List<String> getoriginBuildingIds() {
        return originBuildingIds;
    }

    public BuildingConfig getBuildingConfig() {
        return BuildingManager.i().getBuildingConfig(this);
    }

    // 机器所在的建筑单元UUID
    public String getUnitUUID() {
        return unitUUID;
    }

    // 机器的UUID
    // public String getMachineUUID() {
    // return machineUUID;
    // }

    public Location getOrigin() {
        return BuildingUnitAPI.getUnit(unitUUID).getOriginLocation();
    }

    public int getRotate() {
        return BuildingUnitAPI.getUnit(unitUUID).getRotate();
    }

    public Location getCenter() {
        return BuildingUnitAPI.getUnit(unitUUID).getMaxLocation()
                .add(BuildingUnitAPI.getUnit(unitUUID).getMinLocation())
                .multiply(0.5);
    }

    public Location getMinLocation() {
        return BuildingUnitAPI.getUnit(unitUUID).getMinLocation();
    }

    public Location getMaxLocation() {
        return BuildingUnitAPI.getUnit(unitUUID).getMaxLocation();
    }
}
