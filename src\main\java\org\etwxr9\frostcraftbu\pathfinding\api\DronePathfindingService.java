package org.etwxr9.frostcraftbu.pathfinding.api;

import de.bsommerfeld.pathetic.api.factory.PathfinderFactory;
import de.bsommerfeld.pathetic.api.factory.PathfinderInitializer;
import de.bsommerfeld.pathetic.api.pathing.Pathfinder;
import de.bsommerfeld.pathetic.api.pathing.configuration.PathfinderConfiguration;
import de.bsommerfeld.pathetic.api.pathing.result.Path;
import de.bsommerfeld.pathetic.api.pathing.result.PathfinderResult;
import de.bsommerfeld.pathetic.api.wrapper.PathPosition;
import de.bsommerfeld.pathetic.bukkit.context.BukkitEnvironmentContext;
import de.bsommerfeld.pathetic.bukkit.initializer.BukkitPathfinderInitializer;
import de.bsommerfeld.pathetic.bukkit.mapper.BukkitMapper;
import de.bsommerfeld.pathetic.bukkit.provider.LoadingNavigationPointProvider;
import de.bsommerfeld.pathetic.engine.factory.AStarPathfinderFactory;

import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.plugin.Plugin;
import org.etwxr9.frostcraftbu.pathfinding.PatheticPlugin;
import org.etwxr9.frostcraftbu.pathfinding.processor.DroneCostProcessor;
import org.etwxr9.frostcraftbu.pathfinding.processor.DroneValidationProcessor;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;

/**
 * 无人机寻路服务
 * 
 * 提供异步的无人机寻路API，支持原始路径和平滑路径返回
 * 这是整个插件的无人机寻路核心服务
 */
public class DronePathfindingService {

    private final Pathfinder pathfinder;
    private final Plugin plugin;

    /**
     * 构造函数
     * 
     * @param plugin 插件实例
     */
    public DronePathfindingService(Plugin plugin) {
        this.plugin = plugin;
        this.pathfinder = initializePathfinder();
    }

    /**
     * 初始化寻路器
     */
    private Pathfinder initializePathfinder() {
        // 创建PathfinderFactory
        PathfinderFactory factory = new AStarPathfinderFactory();

        // 创建初始化器
        PathfinderInitializer initializer = new BukkitPathfinderInitializer();

        // 创建无人机专用配置
        PathfinderConfiguration droneConfiguration = PathfinderConfiguration.builder()
                .provider(new LoadingNavigationPointProvider()) // 支持区块加载
                .fallback(true) // 允许备用策略
                .nodeValidationProcessors(List.of(new DroneValidationProcessor()))
                .nodeCostProcessors(List.of(new DroneCostProcessor()))
                .async(true)
                .maxIterations(150000) // 3D寻路需要更多迭代
                .build();

        return factory.createPathfinder(droneConfiguration, initializer);
    }

    /**
     * 异步寻路（仅返回原始路径）
     *
     * @param start 起点位置
     * @param end   终点位置
     * @return CompletableFuture包装的寻路结果
     */
    public CompletableFuture<DronePathResult> findPathAsync(Location start, Location end) {
        return findPathAsync(start, end, false, PathSmoothingUtil.SmoothingType.CATMULL_ROM, 10, true);
    }

    /**
     * 异步寻路（支持路径平滑）
     *
     * @param start           起点位置
     * @param end             终点位置
     * @param enableSmoothing 是否启用路径平滑
     * @param smoothingType   平滑算法类型
     * @param resolution      平滑分辨率
     * @return CompletableFuture包装的寻路结果
     */
    public CompletableFuture<DronePathResult> findPathAsync(Location start, Location end,
            boolean enableSmoothing,
            PathSmoothingUtil.SmoothingType smoothingType,
            int resolution) {
        return findPathAsync(start, end, enableSmoothing, smoothingType, resolution, true);
    }

    /**
     * 异步寻路（完整参数版本，支持路径预处理）
     *
     * @param start           起点位置
     * @param end             终点位置
     * @param enableSmoothing 是否启用路径平滑
     * @param smoothingType   平滑算法类型
     * @param resolution      平滑分辨率
     * @param preprocessPath  是否对原始路径进行预处理（平滑直角转弯）
     * @return CompletableFuture包装的寻路结果
     */
    public CompletableFuture<DronePathResult> findPathAsync(Location start, Location end,
            boolean enableSmoothing,
            PathSmoothingUtil.SmoothingType smoothingType,
            int resolution,
            boolean preprocessPath) {
        long startTime = System.currentTimeMillis();

        // 输入验证
        if (start == null || end == null) {
            return CompletableFuture.completedFuture(
                    new DronePathResult(DronePathResult.PathState.INVALID_INPUT,
                            "起点或终点不能为null",
                            System.currentTimeMillis() - startTime));
        }

        if (start.getWorld() == null || end.getWorld() == null) {
            return CompletableFuture.completedFuture(
                    new DronePathResult(DronePathResult.PathState.INVALID_INPUT,
                            "起点或终点的世界不能为null",
                            System.currentTimeMillis() - startTime));
        }

        if (!start.getWorld().equals(end.getWorld())) {
            return CompletableFuture.completedFuture(
                    new DronePathResult(DronePathResult.PathState.INVALID_INPUT,
                            "起点和终点必须在同一个世界",
                            System.currentTimeMillis() - startTime));
        }

        // 转换为PathPosition
        PathPosition startPos = BukkitMapper.toPathPosition(start);
        PathPosition endPos = BukkitMapper.toPathPosition(end);
        World world = start.getWorld();

        // 执行异步寻路
        CompletionStage<PathfinderResult> pathfindingStage = pathfinder.findPath(startPos, endPos,
                new BukkitEnvironmentContext(world));

        return pathfindingStage.toCompletableFuture().thenApply(result -> {
            long computeTime = System.currentTimeMillis() - startTime;

            // 处理寻路失败
            if (!result.successful() && !result.hasFallenBack()) {
                DronePathResult.PathState state = DronePathResult.PathState.NO_PATH;
                String errorMsg = "无法找到有效路径: " + result.getPathState().name();
                return new DronePathResult(state, errorMsg, computeTime);
            }

            // 转换路径为Location列表
            List<Location> rawPath = convertPathToLocations(result.getPath(), world);

            // 路径预处理（平滑直角转弯）
            List<Location> processedPath = rawPath;
            if (preprocessPath && rawPath.size() > 2) {
                processedPath = PathSmoothingUtil.preprocessPath(rawPath);
            }

            // 计算路径长度（使用预处理后的路径）
            double pathLength = PathSmoothingUtil.calculatePathLength(processedPath);

            // 处理路径平滑（基于预处理后的路径）
            List<Location> smoothedPath = null;
            if (enableSmoothing && processedPath.size() > 2) {
                smoothedPath = PathSmoothingUtil.smoothPath(processedPath, smoothingType, resolution);
            }

            // 确定状态
            DronePathResult.PathState state = result.hasFallenBack() ? DronePathResult.PathState.FALLBACK
                    : DronePathResult.PathState.SUCCESS;

            return new DronePathResult(state, processedPath, smoothedPath, pathLength, computeTime, start, end);
        }).exceptionally(throwable -> {
            long computeTime = System.currentTimeMillis() - startTime;
            String errorMsg = "寻路过程中发生异常: " + throwable.getMessage();
            return new DronePathResult(DronePathResult.PathState.FAILED, errorMsg, computeTime);
        });
    }

    /**
     * 将Pathetic的Path转换为Bukkit Location列表
     */
    private List<Location> convertPathToLocations(Path path, World world) {
        List<Location> locations = new ArrayList<>();

        path.forEach(pathPosition -> {
            Location location = BukkitMapper.toLocation(pathPosition, world);
            // 调整到方块中心
            location.add(0.5, 0.5, 0.5);
            locations.add(location);
        });

        return locations;
    }

    /**
     * 同步寻路方法（阻塞调用）
     * 注意：这会阻塞当前线程，建议在异步上下文中使用
     */
    public DronePathResult findPathSync(Location start, Location end) {
        try {
            return findPathAsync(start, end).get();
        } catch (Exception e) {
            return new DronePathResult(DronePathResult.PathState.FAILED,
                    "同步寻路异常: " + e.getMessage(), 0);
        }
    }

    /**
     * 同步寻路方法（支持平滑，阻塞调用）
     */
    public DronePathResult findPathSync(Location start, Location end,
            boolean enableSmoothing,
            PathSmoothingUtil.SmoothingType smoothingType,
            int resolution) {
        try {
            return findPathAsync(start, end, enableSmoothing, smoothingType, resolution).get();
        } catch (Exception e) {
            return new DronePathResult(DronePathResult.PathState.FAILED,
                    "同步寻路异常: " + e.getMessage(), 0);
        }
    }

    /**
     * 获取插件实例
     */
    public Plugin getPlugin() {
        return plugin;
    }

    /**
     * 获取底层寻路器实例（高级用法）
     */
    public Pathfinder getPathfinder() {
        return pathfinder;
    }


}
