package org.etwxr9.frostcraftbu.pathfinding.processor;

import de.bsommerfeld.pathetic.api.pathing.context.EnvironmentContext;
import de.bsommerfeld.pathetic.api.pathing.processing.NodeValidationProcessor;
import de.bsommerfeld.pathetic.api.pathing.processing.context.NodeEvaluationContext;
import de.bsommerfeld.pathetic.api.provider.NavigationPointProvider;
import de.bsommerfeld.pathetic.api.wrapper.PathPosition;
import de.bsommerfeld.pathetic.bukkit.provider.BukkitNavigationPoint;
import org.bukkit.Material;

/**
 * 无人机验证处理器，实现无人机特有的移动规则和限制
 * 
 * 无人机物理特性：
 * - 尺寸：1x1x1方块大小
 * - 悬浮高度：相对于底部方块上方1-3格范围内
 * - 不能通过固体方块，除非该方块的isPassable为true
 * 
 * 高度限制规则：
 * - 最大悬浮高度：底部方块上方3格，不能更高
 * - 当处于3格悬浮高度时，不能选择更高的位置
 * - 最低悬浮高度：底部方块上方至少1格
 * - 如果当前高度超过3格，必须优先垂直下降
 */
public class DroneValidationProcessor implements NodeValidationProcessor {

    // 无人机悬浮高度限制
    private static final int MIN_HOVER_HEIGHT = 1;
    private static final int MAX_HOVER_HEIGHT = 3;

    @Override
    public boolean isValid(NodeEvaluationContext context) {
        PathPosition currentPosition = context.getCurrentPathPosition();
        NavigationPointProvider provider = context.getNavigationPointProvider();
        EnvironmentContext environmentContext = context.getEnvironmentContext();

        // 检查当前位置是否可通行（无人机不能穿过固体方块）
        if (!isPositionTraversable(currentPosition, provider, environmentContext)) {
            return false;
        }

        // 检查高度限制
        if (!isHeightValid(currentPosition, provider, environmentContext)) {
            return false;
        }

        // 检查是否有足够的悬浮空间（无人机需要1x1x1的空间）
        if (!hasEnoughSpace(currentPosition, provider, environmentContext)) {
            return false;
        }

        return true;
    }

    /**
     * 检查位置是否可通行
     */
    private boolean isPositionTraversable(PathPosition position, NavigationPointProvider provider, 
                                        EnvironmentContext environmentContext) {
        BukkitNavigationPoint navigationPoint = 
            (BukkitNavigationPoint) provider.getNavigationPoint(position, environmentContext);
        
        if (navigationPoint == null) {
            return false;
        }

        // 无人机不能通过固体方块
        return navigationPoint.isTraversable();
    }

    /**
     * 检查高度是否有效
     */
    private boolean isHeightValid(PathPosition position, NavigationPointProvider provider, 
                                EnvironmentContext environmentContext) {
        // 寻找底部固体方块
        PathPosition groundPosition = findGroundBelow(position, provider, environmentContext);
        
        if (groundPosition == null) {
            // 如果找不到地面，则无效
            return false;
        }

        // 计算相对于地面的高度
        double relativeHeight = position.getY() - groundPosition.getY();
        
        // 检查是否在允许的悬浮高度范围内
        return relativeHeight >= MIN_HOVER_HEIGHT && relativeHeight <= MAX_HOVER_HEIGHT;
    }

    /**
     * 寻找指定位置下方的地面（固体方块）
     */
    private PathPosition findGroundBelow(PathPosition position, NavigationPointProvider provider, 
                                       EnvironmentContext environmentContext) {
        // 从当前位置向下搜索，最多搜索10格
        for (int i = 0; i <= 10; i++) {
            PathPosition checkPosition = position.subtract(0, i, 0);
            BukkitNavigationPoint navigationPoint = 
                (BukkitNavigationPoint) provider.getNavigationPoint(checkPosition, environmentContext);
            
            if (navigationPoint != null && !navigationPoint.isTraversable()) {
                // 找到固体方块，这就是地面
                return checkPosition;
            }
        }
        
        return null; // 没有找到地面
    }

    /**
     * 检查无人机是否有足够的空间（1x1x1）
     */
    private boolean hasEnoughSpace(PathPosition position, NavigationPointProvider provider, 
                                 EnvironmentContext environmentContext) {
        // 无人机占用1x1x1的空间，检查当前位置是否可通行即可
        return isPositionTraversable(position, provider, environmentContext);
    }
}
