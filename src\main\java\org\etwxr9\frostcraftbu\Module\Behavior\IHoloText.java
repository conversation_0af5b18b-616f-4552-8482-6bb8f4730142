package org.etwxr9.frostcraftbu.Module.Behavior;

import org.bukkit.Location;
import net.kyori.adventure.text.Component;

public interface IHoloText {
    /**
     * 获取指定ID的TextDisplay的UUID
     */
    String getTextDisplayUUID(int textId);
    
    /**
     * 设置指定ID的TextDisplay的UUID
     */
    void setTextDisplayUUID(int textId, String uuid);
    
    /**
     * 获取指定ID的TextDisplay的位置
     */
    Location getTextDisplayLocation(int textId);
    
    /**
     * 设置指定ID的TextDisplay的位置
     */
    void setTextDisplayLocation(int textId, Location location);
    
    /**
     * 获取TextDisplay的数量
     */
    int getTextCount();
    
    /**
     * 获取指定ID的TextDisplay的文本内容
     */
    Component getTextContent(int textId);
    
    /**
     * 设置指定ID的TextDisplay的文本内容
     */
    void setTextContent(int textId, Component content);
    
    /**
     * 更新所有TextDisplay
     */
    void updateAllTextDisplays();
    
    /**
     * 更新指定ID的TextDisplay
     */
    void updateTextDisplay(int textId);
}