package org.etwxr9.frostcraftbu.Listener;

import org.bukkit.Location;
import org.bukkit.NamespacedKey;
import org.bukkit.entity.ItemFrame;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.hanging.HangingBreakByEntityEvent;
import org.bukkit.event.player.PlayerInteractEntityEvent;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.inventory.EquipmentSlot;
import org.bukkit.inventory.ItemStack;
import org.bukkit.persistence.PersistentDataType;
import org.etwxr9.frostcraftbu.FrostCraftBU;
import org.etwxr9.frostcraftbu.Building.BuildingManager;
import org.etwxr9.frostcraftbu.Item.ItemManager;
import org.etwxr9.frostcraftbu.Module.ContainerModule;
import org.etwxr9.frostcraftbu.Module.ModuleManager;

public class ContainerInteractListener implements Listener {

    @EventHandler
    public void onHangingBreak(HangingBreakByEntityEvent event) {
        if (!(event.getEntity() instanceof ItemFrame) || !(event.getRemover() instanceof Player)) {
            return;
        }
        ItemFrame frame = (ItemFrame) event.getEntity();
        if (hasSlotId(frame)) {
            ContainerModule containerModule = getContainerModule(frame);
            if (containerModule == null) {
                return;
            }
            event.setCancelled(true);
            handleLeftClick((Player) event.getRemover(), frame, containerModule);
        }
    }

    @EventHandler
    public void onEntityDamage(EntityDamageByEntityEvent event) {
        if (!(event.getEntity() instanceof ItemFrame) || !(event.getDamager() instanceof Player)) {
            return;
        }

        ItemFrame frame = (ItemFrame) event.getEntity();
        if (hasSlotId(frame)) {
            ContainerModule containerModule = getContainerModule(frame);
            if (containerModule == null) {
                return;
            }
            event.setCancelled(true);
            handleLeftClick((Player) event.getDamager(), frame, containerModule);
        }
    }

    @EventHandler
    public void onPlayerInteractEntity(PlayerInteractEntityEvent event) {
        if (event.getHand() != EquipmentSlot.HAND) {
            return;
        }
        if (!(event.getRightClicked() instanceof ItemFrame)) {
            return;
        }

        ItemFrame frame = (ItemFrame) event.getRightClicked();

        if (hasSlotId(frame)) {
            ContainerModule containerModule = getContainerModule(frame);
            if (containerModule == null) {
                return;
            }
            event.setCancelled(true);
            handleRightClick(event.getPlayer(), frame, containerModule);
        }
    }

    private boolean hasSlotId(ItemFrame frame) {
        NamespacedKey key = new NamespacedKey(FrostCraftBU.i(), "slotId");
        return frame.getPersistentDataContainer().has(key, PersistentDataType.INTEGER);
    }

    private void handleLeftClick(Player player, ItemFrame frame, ContainerModule containerModule) {

        int slotId = frame.getPersistentDataContainer()
                .get(new NamespacedKey(FrostCraftBU.i(), "slotId"), PersistentDataType.INTEGER);

        // 检查是否有 canModify 标签
        boolean canModify = containerModule.hasSlotTag(slotId, "canModify");

        // 获取玩家主手物品
        ItemStack handItem = player.getInventory().getItemInMainHand();
        if (handItem.getAmount() == 0) {
            player.sendMessage("§c请手持物品进行操作");
            return;
        }

        String itemId = ItemManager.getItemId(handItem);
        if (itemId == null) {
            player.sendMessage("§c该物品不是注册物品，无法操作");
            return;
        }

        String containerItemId = containerModule.getSlotItemId(slotId);

        // 如果有 canModify 标签且容器为空，设置容器物品类型
        if (canModify && containerItemId == null) {
            containerModule.setSlotItem(slotId, itemId, 0);
            frame.setItem(ItemManager.getItem(itemId));
            player.sendMessage("§a已设置容器接受物品类型为: " + itemId);
            return;
        }

        if (!canModify && containerItemId == null) {
            player.sendMessage("§c该容器未设置接受物品类型");
            return;
        }

        // 原有的放入物品逻辑
        if (containerItemId != null && !containerItemId.equals(itemId)) {
            player.sendMessage("§c该容器只接受 " + containerItemId + " 物品");
            return;
        }

        // 计算可放入数量
        int currentCount = containerModule.getSlotItemCount(slotId);
        int maxCount = containerModule.getSlotMaxCount(slotId);
        int availableSpace = maxCount - currentCount;
        int inputAmount = player.isSneaking() ? 1 : Math.min(handItem.getAmount(), availableSpace);

        if (inputAmount <= 0) {
            player.sendMessage("§c容器已满");
            return;
        }

        // 更新容器和玩家物品
        containerModule.setSlotItem(slotId, itemId, currentCount + inputAmount);
        handItem.setAmount(handItem.getAmount() - inputAmount);

        // player.sendMessage(String.format("§a已放入 %d 个物品，容器现有 %d 个物品",
        //         inputAmount, currentCount + inputAmount));
    }

    private void handleRightClick(Player player, ItemFrame frame, ContainerModule containerModule) {

        int slotId = frame.getPersistentDataContainer()
                .get(new NamespacedKey(FrostCraftBU.i(), "slotId"), PersistentDataType.INTEGER);

        String itemId = containerModule.getSlotItemId(slotId);
        int currentCount = containerModule.getSlotItemCount(slotId);

        // 检查是否有 canModify 标签
        boolean canModify = containerModule.hasSlotTag(slotId, "canModify");

        if (itemId == null || currentCount == 0) {
            if (canModify && itemId != null) {
                // 如果数量为0且可修改，清空容器和展示框
                containerModule.clearSlot(slotId);
                frame.setItem(null);
                player.sendMessage("§a已清空容器设置");
            } else {
                player.sendMessage("§c容器为空");
            }
            return;
        }

        // 计算取出数量
        int outputAmount = player.isSneaking() ? 1 : currentCount;

        // 更新容器
        containerModule.setSlotItem(slotId, itemId, currentCount - outputAmount);

        // // 在展示框前方生成物品
        // Location dropLoc = frame.getLocation().clone().add(frame.getFacing().getDirection().multiply(0.5));
        // ItemStack dropItem = ItemManager.getItem(itemId).clone();
        // dropItem.setAmount(outputAmount);
        // frame.getWorld().dropItem(dropLoc, dropItem);

        // player.sendMessage(String.format("§a已取出 %d 个物品，容器剩余 %d 个物品",
        //         outputAmount, currentCount - outputAmount));
    }

    private ContainerModule getContainerModule(ItemFrame frame) {
        // 获取展示框所在的建筑
        var building = BuildingManager.i().getBuildingAt(frame.getLocation());
        if (building == null) {
            return null;
        }

        // 获取ContainerModule
        var module = building.getModule(ModuleManager.ModuleType.ContainerModule.getName());
        if (module == null) {
            return null;
        } else if (!(module instanceof ContainerModule)) {
            return null;
        }
        return (ContainerModule) module;
    }
}
