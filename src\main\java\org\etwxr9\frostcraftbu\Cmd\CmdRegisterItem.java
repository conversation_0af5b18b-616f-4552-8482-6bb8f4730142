package org.etwxr9.frostcraftbu.Cmd;

import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.etwxr9.frostcraftbu.Item.ItemManager;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class CmdRegisterItem implements SubCmdBase {
    @Override
    public boolean onSubCommand(Player p, String[] args) {
        if (args.length == 0 || args.length > 2) {
            p.sendMessage("§c用法: /fc registeritem <物品ID> - 注册物品");
            p.sendMessage("§c用法: /fc registeritem list - 打开物品图鉴");
            p.sendMessage("§c用法: /fc registeritem get <物品ID> - 获取指定物品");
            p.sendMessage("§c用法: /fc unregisteritem <物品ID> - 注销物品");
            return false;
        }

        if (args[0].equalsIgnoreCase("list")) {
            ItemManager.openItemUI(p);
            return true;
        }

        if (args[0].equalsIgnoreCase("get")) {
            if (args.length != 2) {
                p.sendMessage("§c用法: /fc registeritem get <物品ID>");
                return false;
            }
            ItemStack item = ItemManager.getItem(args[1]);
            if (item == null) {
                p.sendMessage("§c物品不存在");
                return false;
            }
            p.getInventory().addItem(item);
            p.sendMessage("§a已获取物品: " + args[1]);
            return true;
        }

        if (args[0].equalsIgnoreCase("unregisteritem")) {
            if (args.length != 2) {
                p.sendMessage("§c用法: /fc unregisteritem <物品ID>");
                return false;
            }
            ItemManager.playerUnregisterItem(p, args[1]);
            p.sendMessage("§a已注销物品: " + args[1]);
            return true;
        }

        ItemStack itemInHand = p.getInventory().getItemInMainHand();
        if (itemInHand == null || itemInHand.getType().isAir()) {
            p.sendMessage("§c请手持要注册的物品");
            return false;
        }

        String itemId = args[0];
        itemInHand = itemInHand.clone();
        itemInHand.setAmount(1);
        ItemManager.playerRegisterItem(p, itemId, itemInHand);
        return true;
    }

    @Override
    public List<String> onTabComplete(Player p, String[] args) {
        if (args.length == 1) {
            return Arrays.asList("list", "get", "unregisteritem");
        }
        if (args.length == 2 && args[0].equals("get")) {
            return new ArrayList<>(ItemManager.getRegisteredItems().keySet());
        }
        if (args.length == 2 && args[0].equals("unregisteritem")) {
            return new ArrayList<>(ItemManager.getRegisteredItems().keySet());
        }
        return null;
    }
}
