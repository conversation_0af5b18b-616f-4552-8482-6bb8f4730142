package org.etwxr9.frostcraftbu.Recipe;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.YamlConfiguration;
import org.etwxr9.frostcraftbu.FrostCraftBU;

public class RecipeManager {
    private static RecipeManager instance;
    private Map<String, RecipeConfig> recipes = new HashMap<>();
    private Map<String, GatherRecipeConfig> gatherRecipes = new HashMap<>();
    private Map<String, ConsumeRecipeConfig> consumeRecipes = new HashMap<>();

    public static RecipeManager i() {
        if (instance == null) {
            instance = new RecipeManager();
        }
        return instance;
    }

    public void loadRecipes() {
        File pRecipefile = new File(FrostCraftBU.i().getDataFolder(), "Recipe.yml");
        if (!pRecipefile.exists()) {
            FrostCraftBU.i().saveResource("Recipe.yml", false);
        }
        YamlConfiguration pConfig = YamlConfiguration.loadConfiguration(pRecipefile);

        File gRecipefile = new File(FrostCraftBU.i().getDataFolder(), "GatherRecipe.yml");
        if (!gRecipefile.exists()) {
            FrostCraftBU.i().saveResource("GatherRecipe.yml", false);
        }
        YamlConfiguration gConfig = YamlConfiguration.loadConfiguration(gRecipefile);

        File cRecipefile = new File(FrostCraftBU.i().getDataFolder(), "ConsumeRecipe.yml");
        if (!cRecipefile.exists()) {
            FrostCraftBU.i().saveResource("ConsumeRecipe.yml", false);
        }
        YamlConfiguration cConfig = YamlConfiguration.loadConfiguration(cRecipefile);

        for (String key : pConfig.getKeys(false)) {
            ConfigurationSection section = pConfig.getConfigurationSection(key);
            RecipeConfig recipe = new RecipeConfig();
            recipe.setId(key);
            recipe.setName(section.getString("name"));
            recipe.setTime(section.getInt("time"));
            recipe.setSteam(section.getInt("steam"));

            Map<String, Integer> input = new HashMap<>();
            ConfigurationSection inputSection = section.getConfigurationSection("input");
            if (inputSection != null) {
                for (String itemId : inputSection.getKeys(false)) {
                    input.put(itemId, inputSection.getInt(itemId));
                }
            }
            recipe.setInput(input);

            Map<String, Integer> output = new HashMap<>();
            ConfigurationSection outputSection = section.getConfigurationSection("output");
            if (outputSection != null) {
                for (String itemId : outputSection.getKeys(false)) {
                    output.put(itemId, outputSection.getInt(itemId));
                }
            }
            recipe.setOutput(output);

            recipes.put(key, recipe);
        }

        for (String key : gConfig.getKeys(false)) {
            ConfigurationSection section = gConfig.getConfigurationSection(key);
            GatherRecipeConfig recipe = new GatherRecipeConfig();
            recipe.setId(key);
            recipe.setName(section.getString("name"));
            recipe.setTime(section.getInt("time"));
            recipe.setSteam(section.getInt("steam"));

            Map<String, Integer> output = new HashMap<>();
            ConfigurationSection outputSection = section.getConfigurationSection("output");
            if (outputSection != null) {
                for (String itemId : outputSection.getKeys(false)) {
                    output.put(itemId, outputSection.getInt(itemId));
                }
            }
            recipe.setOutput(output);

            gatherRecipes.put(key, recipe);
        }
        for (String key : cConfig.getKeys(false)) {
            ConfigurationSection section = cConfig.getConfigurationSection(key);
            ConsumeRecipeConfig recipe = new ConsumeRecipeConfig();
            recipe.setId(key);
            recipe.setName(section.getString("name"));
            recipe.setTime(section.getInt("time"));

            Map<String, Integer> input = new HashMap<>();
            ConfigurationSection inputSection = section.getConfigurationSection("input");
            if (inputSection != null) {
                for (String itemId : inputSection.getKeys(false)) {
                    input.put(itemId, inputSection.getInt(itemId));
                }
            }
            recipe.setInput(input);
            recipe.setScoreboardValue(section.getInt("scoreboardValue"));

            consumeRecipes.put(key, recipe);
        }
    }

    public RecipeConfig getRecipe(String id) {
        return recipes.get(id);
    }

    public GatherRecipeConfig getGatherRecipe(String id) {
        return gatherRecipes.get(id);
    }

    public ConsumeRecipeConfig getConsumeRecipe(String id) {
        return consumeRecipes.get(id);
    }
}