package org.etwxr9.frostcraftbu.Listener;

import java.util.Optional;

import org.bukkit.Location;
import org.bukkit.block.Sign;
import org.bukkit.block.sign.Side;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.block.Action;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.event.inventory.InventoryAction;
import org.bukkit.event.player.PlayerInteractEvent;
import org.etwxr9.frostcraftbu.FrostCraftBU;
import org.etwxr9.frostcraftbu.Building.BuildingManager;
import org.etwxr9.frostcraftbu.Item.ItemManager;
import org.etwxr9.frostcraftbu.Module.BaseModule;
import org.etwxr9.frostcraftbu.Module.Behavior.IChestUI;
import org.etwxr9.frostcraftbu.Module.Behavior.IProduction;
import org.etwxr9.frostcraftbu.Module.Behavior.IResearch;
import org.ipvp.canvas.slot.ClickOptions;
import org.ipvp.canvas.type.ChestMenu;

public class ResearchSignListener {
    @EventHandler
    public void onRClickSign(PlayerInteractEvent e) {
        if (e.getAction() != Action.RIGHT_CLICK_BLOCK)
            return;

        var signBlock = e.getClickedBlock();
        if (!(signBlock.getState() instanceof Sign))
            return;

        var signState = (Sign) signBlock.getState();
        String[] signData = signState.getSide(Side.FRONT).getLines();

        // 检查牌子第一行是否符合配置
        if (!signData[0].equals(FrostCraftBU.i().getConfig().getString("ResearchSign0")))
            return;

        e.setCancelled(true);
        Player p = e.getPlayer();

        // 获取建筑
        Location buildingLocation = signBlock.getLocation();
        var building = BuildingManager.i().getBuildingAt(buildingLocation);
        if (building == null) {
            p.sendMessage("该牌子不在任何建筑内");
            return;
        }

        // 检查建筑是否有研究模块
        Optional<BaseModule> researchModuleOpt = building.getModules().values().stream()
                .filter(module -> module instanceof IResearch)
                .findFirst();

        if (researchModuleOpt.isEmpty()) {
            p.sendMessage("该建筑没有研究模块");
            return;
        }
        BaseModule researchModule = researchModuleOpt.get();
        // 创建产线选择菜单
        if (!(researchModule instanceof IChestUI)) {
            p.sendMessage("该建筑的研究模块没有实现IChestUI接口");
            return;
        }
        ((IChestUI) researchModule).getUI().open(p);
    }
}
