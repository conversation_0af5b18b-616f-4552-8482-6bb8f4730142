package org.etwxr9;

import static org.junit.Assert.assertTrue;

import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.junit.Test;

/**
 * Unit test for simple App.
 */
public class AppTest {
    /**
     * Rigorous Test :-)
     */
    @Test
    public void shouldAnswerWithTrue() {
        assertTrue(true);
    }

    @Test
    public void convertLoc() {
        var loc = new Location(null, 1, 2, 3, 4, 5);
        var locS = loc.toString();
        //log
        System.out.println(locS);
    }

}
