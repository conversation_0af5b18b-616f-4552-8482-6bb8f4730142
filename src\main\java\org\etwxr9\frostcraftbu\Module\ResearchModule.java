package org.etwxr9.frostcraftbu.Module;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonTypeName;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextDecoration;

import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.entity.Entity;
import org.bukkit.entity.TextDisplay;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.scoreboard.Objective;
import org.bukkit.scoreboard.Score;
import org.bukkit.scoreboard.Scoreboard;
import org.etwxr9.frostcraftbu.FrostCraftBU;
import org.etwxr9.frostcraftbu.Building.FCBuilding;
import org.etwxr9.frostcraftbu.Item.ItemManager;
import org.etwxr9.frostcraftbu.Module.Behavior.IChestUI;
import org.etwxr9.frostcraftbu.Module.Behavior.IResearch;
import org.etwxr9.frostcraftbu.Tech.TechTreeManager;
import org.ipvp.canvas.Menu;
import org.ipvp.canvas.mask.BinaryMask;
import org.ipvp.canvas.mask.Mask;
import org.ipvp.canvas.paginate.PaginatedMenuBuilder;
import org.ipvp.canvas.slot.SlotSettings;
import org.ipvp.canvas.type.ChestMenu;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.logging.Level;
import java.util.stream.Collectors;

@JsonTypeName("research")
public class ResearchModule extends BaseModule implements IChestUI, IResearch {

    private ContainerModule containerModule;
    private int tickCounter = 0;
    private String researchTextDisplayUUID;

    public String getResearchTextDisplayUUID() {
        return researchTextDisplayUUID;
    }

    public void setResearchTextDisplayUUID(String researchTextDisplayUUID) {
        this.researchTextDisplayUUID = researchTextDisplayUUID;
    }

    private static final int TICKS_PER_PROCESS = 20; // 每秒处理一次

    public ResearchModule(FCBuilding building) {
        super(building);
    }

    @Override
    public String getModuleTypeId() {
        return ModuleManager.ModuleType.ResearchModule.getName();
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ResearchModuleSaveData extends BaseModuleSaveData {
        private String researchTextDisplayUUID;

        @Override
        public String getModuleTypeId() {
            return ModuleManager.ModuleType.ResearchModule.getName();
        }

        public void setResearchTextDisplayUUID(String researchTextDisplayUUID2) {
            this.researchTextDisplayUUID = researchTextDisplayUUID2;
        }

        public String getResearchTextDisplayUUID() {
            return researchTextDisplayUUID;
        }
    }

    @Override
    public BaseModuleSaveData getSaveData() {
        ResearchModuleSaveData data = new ResearchModuleSaveData();
        data.setResearchTextDisplayUUID(researchTextDisplayUUID);
        return data;
    }

    @Override
    public void loadSaveData(BaseModuleSaveData data) {
        if (data instanceof ResearchModuleSaveData) {
            ResearchModuleSaveData saveData = (ResearchModuleSaveData) data;
            researchTextDisplayUUID = saveData.getResearchTextDisplayUUID();
        }
    }

    @Override
    public ItemStack getIcon() {
        ItemStack icon = ItemManager.getItem("researchUI");
        return icon;
    }

    @Override
    public Menu getUI() {
        // 创建主菜单
        ChestMenu mainMenu = ChestMenu.builder(3)
                .title("研究所主菜单")
                .build();

        // 获取材料名称
        String woodMaterialName = FrostCraftBU.i().getConfig().getString("ScoreboardObjectives.WoodMaterialName", "木板");
        String steelMaterialName = FrostCraftBU.i().getConfig().getString("ScoreboardObjectives.SteelMaterialName",
                "钢材");

        // 科技菜单入口按钮
        ItemStack techMenuBtn = ItemManager.getItem("researchUI");
        ItemMeta techMeta = techMenuBtn.getItemMeta();
        if (techMeta != null) {
            techMeta.displayName(Component.text("科技研究菜单").color(NamedTextColor.GOLD)
                    .decoration(TextDecoration.ITALIC, false));
            techMenuBtn.setItemMeta(techMeta);
        }
        mainMenu.getSlot(4).setItem(techMenuBtn);
        mainMenu.getSlot(4).setClickHandler((player, info) -> {
            TechTreeManager manager = TechTreeManager.i();
            String currentProject = manager.getCurrentResearchProject();

            if (currentProject == null) {
                createTechSelectionUI(manager).open(player);
            } else {
                createResearchStatusUI(manager, currentProject).open(player);
            }
        });

        // 木材按钮
        ItemStack woodBtn = ItemManager.getItem(woodMaterialName);
        if (woodBtn == null)
            woodBtn = new ItemStack(Material.OAK_PLANKS);
        ItemMeta woodMeta = woodBtn.getItemMeta();
        if (woodMeta != null) {
            woodMeta.displayName(Component.text("设置接受材料为" + woodMaterialName).color(NamedTextColor.GREEN)
                    .decoration(TextDecoration.ITALIC, false));
            woodBtn.setItemMeta(woodMeta);
        }
        mainMenu.getSlot(11).setItem(woodBtn);
        mainMenu.getSlot(11).setClickHandler((player, info) -> {
            mainMenu.close(player);
            setupContainerSlot(woodMaterialName);
            player.sendMessage(Component.text("已设置容器栏位为" + woodMaterialName).color(NamedTextColor.GREEN));
        });

        // 钢材按钮
        ItemStack steelBtn = ItemManager.getItem(steelMaterialName);
        if (steelBtn == null)
            steelBtn = new ItemStack(Material.IRON_INGOT);
        ItemMeta steelMeta = steelBtn.getItemMeta();
        if (steelMeta != null) {
            steelMeta.displayName(Component.text("设置接受材料为" + steelMaterialName).color(NamedTextColor.GREEN)
                    .decoration(TextDecoration.ITALIC, false));
            steelBtn.setItemMeta(steelMeta);
        }
        mainMenu.getSlot(15).setItem(steelBtn);
        mainMenu.getSlot(15).setClickHandler((player, info) -> {
            mainMenu.close(player);
            setupContainerSlot(steelMaterialName);
            player.sendMessage(Component.text("已设置容器栏位为" + steelMaterialName).color(NamedTextColor.GREEN));
        });

        return mainMenu;
    }

    private Menu createTechSelectionUI(TechTreeManager manager) {
        List<TechTreeManager.Tech> availableTechs = manager.getTechs().values().stream()
                .filter(tech -> !manager.isUnlocked(tech.getName())) // 过滤已解锁的科技
                .filter(tech -> tech.getDependencies().stream().allMatch(manager::isUnlocked)) // 过滤前置条件未满足的科技
                .collect(Collectors.toList());

        // 定义科技项目放置的槽位掩码（1-5行）
        Mask itemMask = BinaryMask.builder(ChestMenu.builder(6).build())
                .pattern("111111111")
                .pattern("111111111")
                .pattern("111111111")
                .pattern("111111111")
                .pattern("111111111")
                .pattern("000000000") // 最后一行保留给控制按钮
                .build();

        // 创建分页菜单
        PaginatedMenuBuilder builder = PaginatedMenuBuilder.builder(ChestMenu.builder(3).title("选择研究项目").redraw(true))
                .slots(itemMask)
                .previousButton(createNamedItem(Material.ARROW, "上一页"))
                .previousButtonSlot(48)
                .nextButton(createNamedItem(Material.ARROW, "下一页"))
                .nextButtonSlot(50);

        // 添加科技项目
        for (TechTreeManager.Tech tech : availableTechs) {
            ItemStack techIcon = createTechItem(tech, manager);
            builder.addItem(SlotSettings.builder()
                    .item(techIcon)
                    .clickHandler((player, clickInfo) -> {
                        if (manager.setCurrentResearchProject(tech.getName())) {
                            player.sendMessage(Component.text("已开始研究: " + tech.getName()).color(NamedTextColor.GREEN));
                            player.closeInventory();
                        } else {
                            player.sendMessage(Component.text("无法开始研究: " + tech.getName() + " (可能已被其他玩家选择或条件不满足)")
                                    .color(NamedTextColor.RED));
                        }
                    })
                    .build());
        }

        return builder.build().get(0);
    }

    private void setupContainerSlot(String materialName) {
        if (containerModule == null)
            return;
        containerModule.setSlotItem(1, materialName, 0);
    }

    private Menu createResearchStatusUI(TechTreeManager manager, String currentProjectName) {
        TechTreeManager.Tech currentTech = manager.getTechByName(currentProjectName);
        if (currentTech == null) {
            manager.setCurrentResearchProject(null);
            return createTechSelectionUI(manager);
        }

        Map<String, Integer> required = currentTech.getConsume();
        Map<String, Integer> provided = manager.getResearchMaterials();

        ChestMenu ui = ChestMenu.builder(1)
                .title("研究: " + currentProjectName)
                .build();

        // 创建进度显示物品
        ItemStack progressItem = new ItemStack(Material.BOOK);
        ItemMeta meta = progressItem.getItemMeta();
        if (meta != null) {
            meta.displayName(Component.text("研究进度").color(NamedTextColor.GOLD)
                    .decoration(TextDecoration.ITALIC, false));
            List<Component> lore = new ArrayList<>();
            lore.add(Component.text("当前研究: " + currentProjectName).color(NamedTextColor.WHITE)
                    .decoration(TextDecoration.ITALIC, false));
            lore.add(Component.text("所需材料:").color(NamedTextColor.WHITE)
                    .decoration(TextDecoration.ITALIC, false));

            boolean complete = true;
            if (required != null && !required.isEmpty()) {
                for (Map.Entry<String, Integer> reqEntry : required.entrySet()) {
                    String itemId = reqEntry.getKey();
                    int reqAmount = reqEntry.getValue();
                    int provAmount = provided.getOrDefault(itemId, 0);
                    ItemStack reqItem = ItemManager.getItem(itemId);
                    Component itemName = (reqItem != null && reqItem.hasItemMeta()
                            && reqItem.getItemMeta().hasDisplayName())
                                    ? reqItem.displayName()
                                    : Component.text(itemId);
                    Component line = Component.text("  - ").color(NamedTextColor.WHITE)
                            .decoration(TextDecoration.ITALIC, false)
                            .append(itemName.color(NamedTextColor.WHITE))
                            .append(Component.text(": " + provAmount + " / " + reqAmount).color(NamedTextColor.WHITE));
                    if (provAmount < reqAmount) {
                        complete = false;
                        line = line.color(NamedTextColor.RED);
                    } else {
                        line = line.color(NamedTextColor.GREEN);
                    }
                    lore.add(line);
                }
            }

            lore.add(Component.text(""));
            lore.add(Component.text("请将材料放入研究站的输入槽中").color(NamedTextColor.YELLOW)
                    .decoration(TextDecoration.ITALIC, false));

            meta.lore(lore);
            progressItem.setItemMeta(meta);
        }
        ui.getSlot(0).setItem(progressItem);

        // 取消研究按钮
        ItemStack cancelBtn = new ItemStack(Material.BARRIER);
        ItemMeta cancelMeta = cancelBtn.getItemMeta();
        if (cancelMeta != null) {
            cancelMeta.displayName(
                    Component.text("取消当前研究").color(NamedTextColor.RED).decoration(TextDecoration.ITALIC, false));
            List<Component> lore = new ArrayList<>();
            lore.add(Component.text("注意: 已提交的材料不会返还!").color(NamedTextColor.YELLOW).decoration(TextDecoration.ITALIC,
                    false));
            cancelMeta.lore(lore);
            cancelBtn.setItemMeta(cancelMeta);
        }
        ui.getSlot(8).setItem(cancelBtn);
        ui.getSlot(8).setClickHandler((p, clickInfo) -> {
            manager.setCurrentResearchProject(null);
            p.sendMessage(Component.text("已取消当前研究项目。").color(NamedTextColor.YELLOW));
            getUI().open(p);
        });

        return ui;
    }

    private ItemStack createTechItem(TechTreeManager.Tech tech, TechTreeManager manager) {
        ItemStack icon = new ItemStack(Material.PAPER);
        ItemMeta meta = icon.getItemMeta();
        if (meta != null) {
            meta.displayName(Component.text(tech.getName()).color(NamedTextColor.GOLD)
                    .decoration(TextDecoration.ITALIC, false));
            List<Component> lore = new ArrayList<>();

            // // 添加描述
            // if (tech.getDescription() != null && !tech.getDescription().isEmpty()) {
            // lore.add(Component.text(tech.getDescription()).color(NamedTextColor.GRAY)
            // .decoration(TextDecoration.ITALIC, false));
            // lore.add(Component.text(""));
            // }

            // 添加前置科技
            if (!tech.getDependencies().isEmpty()) {
                lore.add(Component.text("前置科技:").color(NamedTextColor.WHITE)
                        .decoration(TextDecoration.ITALIC, false));
                for (String dep : tech.getDependencies()) {
                    Component depLine = Component.text("  - " + dep).decoration(TextDecoration.ITALIC, false);
                    if (manager.isUnlocked(dep)) {
                        depLine = depLine.color(NamedTextColor.GREEN);
                    } else {
                        depLine = depLine.color(NamedTextColor.RED);
                    }
                    lore.add(depLine);
                }
                lore.add(Component.text(""));
            }

            // 添加所需材料
            lore.add(Component.text("所需材料:").color(NamedTextColor.WHITE)
                    .decoration(TextDecoration.ITALIC, false));
            for (Map.Entry<String, Integer> entry : tech.getConsume().entrySet()) {
                lore.add(Component.text("  - " + entry.getKey() + ": " + entry.getValue())
                        .color(NamedTextColor.YELLOW).decoration(TextDecoration.ITALIC, false));
            }

            meta.lore(lore);
            icon.setItemMeta(meta);
        }
        return icon;
    }

    private ItemStack createNamedItem(Material material, String name) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        if (meta != null) {
            meta.displayName(Component.text(name).decoration(TextDecoration.ITALIC, false));
            item.setItemMeta(meta);
        }
        return item;
    }

    @Override
    public void onLoad(boolean first) {
        containerModule = (ContainerModule) building.getModule("container");
        if (containerModule == null) {
            throw new IllegalStateException("ResearchModule requires ContainerModule");
        }
        if (first) {
            // 如果有当前研究项目，设置容器接受的物品类型
            // String currentProject = TechTreeManager.i().getCurrentResearchProject();

            // if (currentProject != null) {
            // TechTreeManager.Tech tech =
            // TechTreeManager.i().getTechByName(currentProject);
            // if (tech != null) {
            // }
            // }

            // 获取研究所的TextDisplay
            var minLoc = building.getMinLocation();
            var maxLoc = building.getMaxLocation();
            // 获取球形范围内的TextDisplay实体
            double radius = Math.max(maxLoc.distance(minLoc) / 2, 1);
            var nearbyEntities = minLoc.getWorld().getNearbyEntities(
                    minLoc.clone().add(maxLoc).multiply(0.5), radius, radius, radius,
                    entity -> entity instanceof TextDisplay);

            // 对实体进行矩形判断
            for (Entity entity : nearbyEntities) {
                Location entityLoc = entity.getLocation();
                if (entityLoc.getX() >= minLoc.getX() && entityLoc.getX() <= maxLoc.getX() &&
                        entityLoc.getY() >= minLoc.getY() && entityLoc.getY() <= maxLoc.getY() &&
                        entityLoc.getZ() >= minLoc.getZ() && entityLoc.getZ() <= maxLoc.getZ()) {

                    if (!(entity instanceof TextDisplay)) {
                        continue;
                    }
                    TextDisplay textDisplay = (TextDisplay) entity;
                    // // log
                    // FrostCraftBU.i().getLogger().info("Found TextDisplay at " + entityLoc + "
                    // with tags "
                    // + textDisplay.getScoreboardTags().toString() + "with text: " +
                    // textDisplay.text());
                    if (textDisplay.getScoreboardTags().contains("research_display")) {
                        researchTextDisplayUUID = textDisplay.getUniqueId().toString();
                    }
                }
            }
        }

        // 每秒设定displayText
        Bukkit.getScheduler().runTaskTimer(FrostCraftBU.i(), () -> {
            if (researchTextDisplayUUID != null) {
                Entity displayE = Bukkit.getEntity(UUID.fromString(researchTextDisplayUUID));
                if (displayE != null) {
                    TextDisplay textDisplayE = (TextDisplay) displayE;
                    String text = TechTreeManager.i().getCurrentResearchProject() == null ? "当前无研究项目"
                            : "当前研究: " + TechTreeManager.i().getCurrentResearchProject();
                    if (TechTreeManager.i().getJustFinishResearchProject() != null) {
                        text = "研究完成: " + TechTreeManager.i().getJustFinishResearchProject();
                    }
                    textDisplayE.text(Component.text(text));
                }
            }
        }, 0L, 20L);
    }

    @Override
    public void onUnload() {
        // 无需特殊处理
    }

    @Override
    public void tick() {
        tickCounter++;
        if (tickCounter >= TICKS_PER_PROCESS) {
            tickCounter = 0;
            processResearchMaterials();
        }
    }

    @Override
    public String getCurrentResearchProject() {
        return TechTreeManager.i().getCurrentResearchProject();
    }

    @Override
    public boolean setCurrentResearchProject(String techName) {
        TechTreeManager manager = TechTreeManager.i();
        boolean result = manager.setCurrentResearchProject(techName);

        return result;
    }

    @Override
    public boolean isResearchCompleted() {
        return TechTreeManager.i().getCurrentResearchProject() == null;
    }

    @Override
    public void processResearchMaterials() {
        if (containerModule == null)
            return;

        TechTreeManager manager = TechTreeManager.i();
        String currentProject = manager.getCurrentResearchProject();
        if (currentProject == null)
            return;

        TechTreeManager.Tech tech = manager.getTechByName(currentProject);
        if (tech == null)
            return;

        // 获取输入槽
        List<Integer> inputSlots = containerModule.getSlotsByTag("input");
        if (inputSlots.isEmpty())
            return;

        // 检查蒸汽
        var comsumeSteamobj = FrostCraftBU.i().getConfig().getString("ScoreboardObjectives.SteamObj");
        var comsumeSteamEntry = FrostCraftBU.i().getConfig().getString("ScoreboardObjectives.SteamEntry");
        var comsumeSteamValue = FrostCraftBU.i().getConfig().getInt("ScoreboardObjectives.ResearchConsumeSteam");
        if (comsumeSteamobj != null && comsumeSteamEntry != null) {
            try {
                Scoreboard scoreboard = Bukkit.getScoreboardManager().getMainScoreboard();
                Objective objective = scoreboard.getObjective(comsumeSteamobj);
                if (objective != null) {
                    Score score = objective.getScore(comsumeSteamEntry);
                    if (score.getScore() < comsumeSteamValue) {
                        return;
                    }
                } else {
                    FrostCraftBU.i().getLogger().warning("记分板目标 '" + comsumeSteamobj + "' 不存在！请在游戏中创建它。");
                }
            } catch (Exception e) {
                FrostCraftBU.i().getLogger().log(Level.SEVERE, "设置记分板分数时出错 (" + comsumeSteamobj + "): ", e);
            }
        }

        // 检查每个输入槽，消耗材料并添加到研究池
        boolean materialsProcessed = false;

        for (int slotId : inputSlots) {
            String itemId = containerModule.getSlotItemId(slotId);
            int count = containerModule.getSlotItemCount(slotId);

            if (itemId != null && count > 0 && tech.getConsume().containsKey(itemId)
                    && tech.getConsume().get(itemId) > TechTreeManager.i().getResearchMaterials().getOrDefault(itemId,
                            0)) {
                // 每次只消耗一个物品
                containerModule.cosumeItem(slotId, 1);
                manager.addResearchMaterial(itemId, 1);
                materialsProcessed = true;

                continue;
            }
        }

        // 消耗蒸汽
        if (materialsProcessed) {
            if (comsumeSteamobj != null && comsumeSteamEntry != null) {
                try {
                    Scoreboard scoreboard = Bukkit.getScoreboardManager().getMainScoreboard();
                    Objective objective = scoreboard.getObjective(comsumeSteamobj);
                    if (objective != null) {
                        Score score = objective.getScore(comsumeSteamEntry);
                        score.setScore(score.getScore() - comsumeSteamValue);
                    } else {
                        FrostCraftBU.i().getLogger().warning("记分板目标 '" + comsumeSteamobj + "' 不存在！请在游戏中创建它。");
                    }
                } catch (Exception e) {
                    FrostCraftBU.i().getLogger().log(Level.SEVERE, "设置记分板分数时出错 (" + comsumeSteamobj + "): ", e);
                }
            }
        }
    }
}
