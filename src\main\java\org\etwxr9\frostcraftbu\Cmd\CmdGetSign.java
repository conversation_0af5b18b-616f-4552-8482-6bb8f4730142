package org.etwxr9.frostcraftbu.Cmd;

import org.bukkit.Material;
import org.bukkit.block.BlockState;
import org.bukkit.block.Sign;
import org.bukkit.block.sign.Side;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.BlockStateMeta;
import org.etwxr9.frostcraftbu.FrostCraftBU;
import org.etwxr9.frostcraftbu.Building.BuildingManager;

import java.util.ArrayList;
import java.util.List;

//给予玩家牌子，牌子的种类依照第一个参数而定。
//牌子的种类包括：建筑模板牌，配方选择牌
//建筑模板牌的第二个参数为建筑名称
public class CmdGetSign implements SubCmdBase {
    @Override
    public boolean onSubCommand(Player p, String[] args) {

        // 判断第一个参数
        // 建筑模板牌
        if (args[0].equals("建筑模板牌") && args.length == 2) {
            p.sendMessage("给予建筑模板牌子");
            var signItem = new ItemStack(Material.OAK_SIGN);
            var itemMeta = signItem.getItemMeta();
            if (!(itemMeta instanceof BlockStateMeta))
                return false;
            BlockStateMeta blockStateMeta = (BlockStateMeta) itemMeta;
            BlockState blockState = blockStateMeta.getBlockState();
            if (!(blockState instanceof Sign))
                return false;
            Sign sign = (Sign) blockState;
            sign.getSide(Side.FRONT).setLine(0, FrostCraftBU.i().getConfig().getString("SaveBuildingSign0"));
            sign.getSide(Side.FRONT).setLine(1, args[1]);
            sign.update();
            blockStateMeta.setBlockState(sign);
            blockStateMeta.setDisplayName("模板牌:" + args[1]);
            signItem.setItemMeta(blockStateMeta);
            signItem.setAmount(1);
            p.getInventory().addItem(signItem);
        }
        // 建筑牌
        else if (args[0].equals("空地建筑牌") && args.length >= 2) {
            p.sendMessage("给予建筑牌子");
            var signItem = new ItemStack(Material.OAK_SIGN);
            var itemMeta = signItem.getItemMeta();
            if (!(itemMeta instanceof BlockStateMeta))
                return false;
            BlockStateMeta blockStateMeta = (BlockStateMeta) itemMeta;
            BlockState blockState = blockStateMeta.getBlockState();
            if (!(blockState instanceof Sign))
                return false;
            Sign sign = (Sign) blockState;
            sign.getSide(Side.FRONT).setLine(0, FrostCraftBU.i().getConfig().getString("EmptyLandSign0"));
            sign.getSide(Side.FRONT).setLine(1, FrostCraftBU.i().getConfig().getString("EmptyLandSign1"));
            var landTypeNames = FrostCraftBU.i().getConfig().getConfigurationSection("EmptyLandSign2List");
            if (landTypeNames.contains(args[1])) {
                sign.getSide(Side.FRONT).setLine(2, args[1]);
            } else {
                sign.getSide(Side.FRONT).setLine(2, "小地皮");
            }

            sign.update();
            blockStateMeta.setBlockState(sign);
            blockStateMeta.setDisplayName("建筑牌");
            signItem.setItemMeta(blockStateMeta);
            signItem.setAmount(1);
            p.getInventory().addItem(signItem);

        }
        // 建筑管理牌
        else if (args[0].equals("建筑管理牌")) {
            p.sendMessage("给予建筑管理牌子");
            var signItem = new ItemStack(Material.OAK_SIGN);
            var itemMeta = signItem.getItemMeta();
            if (!(itemMeta instanceof BlockStateMeta))
                return false;
            BlockStateMeta blockStateMeta = (BlockStateMeta) itemMeta;
            BlockState blockState = blockStateMeta.getBlockState();
            if (!(blockState instanceof Sign))
                return false;
            Sign sign = (Sign) blockState;
            sign.getSide(Side.FRONT).setLine(0, FrostCraftBU.i().getConfig().getString("OccupiedLandSign0"));
            sign.getSide(Side.FRONT).setLine(1, FrostCraftBU.i().getConfig().getString("OccupiedLandSign1"));
            sign.update();
            blockStateMeta.setBlockState(sign);
            blockStateMeta.setDisplayName("管理牌");
            signItem.setItemMeta(blockStateMeta);
            signItem.setAmount(1);
            p.getInventory().addItem(signItem);
        }
        // 配方选择牌
        else if (args[0].equals("配方选择牌")) {
            p.sendMessage("给予配方选择牌子");
            var signItem = new ItemStack(Material.OAK_SIGN);
            var itemMeta = signItem.getItemMeta();
            if (!(itemMeta instanceof BlockStateMeta))
                return false;
            BlockStateMeta blockStateMeta = (BlockStateMeta) itemMeta;
            BlockState blockState = blockStateMeta.getBlockState();
            if (!(blockState instanceof Sign))
                return false;
            Sign sign = (Sign) blockState;
            sign.getSide(Side.FRONT).setLine(0, FrostCraftBU.i().getConfig().getString("ProductionSign0"));
            sign.getSide(Side.FRONT).setLine(1, FrostCraftBU.i().getConfig().getString("ProductionSign1"));
            sign.update();
            blockStateMeta.setBlockState(sign);
            blockStateMeta.setDisplayName("配方牌");
            signItem.setItemMeta(blockStateMeta);
            signItem.setAmount(1);
            p.getInventory().addItem(signItem);
        }
        // 采集牌子
        else if (args[0].equals("采集选择牌")) {
            p.sendMessage("给予采集选择牌子");
            var signItem = new ItemStack(Material.OAK_SIGN);
            var itemMeta = signItem.getItemMeta();
            if (!(itemMeta instanceof BlockStateMeta))
                return false;
            BlockStateMeta blockStateMeta = (BlockStateMeta) itemMeta;
            BlockState blockState = blockStateMeta.getBlockState();
            if (!(blockState instanceof Sign))
                return false;
            Sign sign = (Sign) blockState;
            sign.getSide(Side.FRONT).setLine(0, FrostCraftBU.i().getConfig().getString("GatherSign0"));
            sign.getSide(Side.FRONT).setLine(1, FrostCraftBU.i().getConfig().getString("GatherSign1"));
            sign.update();
            blockStateMeta.setBlockState(sign);
            blockStateMeta.setDisplayName("采集牌");
            signItem.setItemMeta(blockStateMeta);
            signItem.setAmount(1);
            p.getInventory().addItem(signItem);
        }
        // 消耗牌子
        else if (args[0].equals("消耗选择牌")) {
            p.sendMessage("给予消耗选择牌子");
            var signItem = new ItemStack(Material.OAK_SIGN);
            var itemMeta = signItem.getItemMeta();
            if (!(itemMeta instanceof BlockStateMeta))
                return false;
            BlockStateMeta blockStateMeta = (BlockStateMeta) itemMeta;
            BlockState blockState = blockStateMeta.getBlockState();
            if (!(blockState instanceof Sign))
                return false;
            Sign sign = (Sign) blockState;
            sign.getSide(Side.FRONT).setLine(0, FrostCraftBU.i().getConfig().getString("ConsumeSign0"));
            sign.getSide(Side.FRONT).setLine(1, FrostCraftBU.i().getConfig().getString("ConsumeSign1"));
            sign.update();
            blockStateMeta.setBlockState(sign);
            blockStateMeta.setDisplayName("消耗牌");
            signItem.setItemMeta(blockStateMeta);
            signItem.setAmount(1);
            p.getInventory().addItem(signItem);
        }

        return true;
    }

    // 参数补全
    public List<String> onTabComplete(Player p, String[] args) {
        if (args.length == 1) {
            return List.of("空地建筑牌", "建筑模板牌","建筑管理牌", "配方选择牌", "采集选择牌", "消耗选择牌");
        } else if (args.length == 2 && args[0].equals("空地建筑牌")) {
            return FrostCraftBU.i().getConfig().getConfigurationSection("EmptyLandSign2List").getKeys(false).stream()
                    .toList();
        }
        // 建筑模板牌
        else if (args.length == 2 && args[0].equals("建筑模板牌")) {
            return BuildingManager.i().getBuildingConfigMap().keySet().stream().toList();
        } else if (args.length == 2 && args[0].equals("解锁牌")) {
            return List.of("1", "2", "3");
        } else if (args.length == 2 && args[0].equals("升级牌")) {
            var upgradeM = new ArrayList<String>();
            BuildingManager.i().getBuildingConfigMap().values().forEach(m -> {
                if (m.getAllModuleConfig().containsKey("upgrade")) {
                    upgradeM.add(m.getId());
                }
            });
            return upgradeM;
        }
        return null;
    }
}
