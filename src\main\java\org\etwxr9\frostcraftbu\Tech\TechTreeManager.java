package org.etwxr9.frostcraftbu.Tech;

import org.bukkit.Bukkit;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.scoreboard.Objective;
import org.bukkit.scoreboard.Score;
import org.bukkit.scoreboard.Scoreboard;
import org.etwxr9.frostcraftbu.FrostCraftBU;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class TechTreeManager {
    private static TechTreeManager instance;
    private final Map<String, Tech> techs = new HashMap<>();

    // 在类的顶部添加常量
    private static final int TECH_STATUS_NONE = 0; // 未选择
    private static final int TECH_STATUS_RESEARCHING = 1; // 正在研究
    private static final int TECH_STATUS_UNLOCKED = 2; // 已解锁

    public Map<String, Tech> getTechs() {
        return techs;
    }

    // Research Fields
    private String currentResearchProject = null;
    // 研究完成后5秒内设置研究所文字显示
    private String justFinishResearchProject = null;

    // Use ConcurrentHashMap for potential async access later if needed
    private final Map<String, Integer> currentResearchMaterials = new ConcurrentHashMap<>();
    private final List<String> UnlockedTechs = new ArrayList<>();

    public void saveResearchData() {
        var researchJsonPath = Path.of(FrostCraftBU.i().getDataFolder().getAbsolutePath() + "/ResearchSaveData.json");
        try {
            ObjectMapper mapper = new ObjectMapper();
            var saveData = new HashMap<String, Object>();
            saveData.put("currentResearchProject", currentResearchProject);
            saveData.put("currentResearchMaterials", currentResearchMaterials);
            saveData.put("UnlockedTechs", UnlockedTechs);

            String result = mapper.writeValueAsString(saveData);
            Files.writeString(researchJsonPath, result);
        } catch (IOException e) {
            FrostCraftBU.i().getLogger().log(Level.SEVERE, "保存研究数据失败", e);
        }
    }

    public void loadResearchData() {
        var researchJsonPath = Path.of(FrostCraftBU.i().getDataFolder().getAbsolutePath() + "/ResearchSaveData.json");
        if (!Files.exists(researchJsonPath)) {
            FrostCraftBU.i().getLogger().info("研究数据文件不存在，将创建新的研究数据");
            return;
        }

        try {
            String jsonContent = Files.readString(researchJsonPath);
            ObjectMapper mapper = new ObjectMapper();

            var saveData = mapper.readValue(jsonContent, new TypeReference<HashMap<String, Object>>() {
            });
            var currentRProject = (String) saveData.get("currentResearchProject");
            if (techs.get(currentRProject) != null) {
                setCurrentResearchProject(currentRProject);
            } else {
                FrostCraftBU.i().getLogger().warning("科技树中不存在存档中的科技: " + currentRProject);
            }
            var materialsMap = (Map<String, Integer>) saveData.get("currentResearchMaterials");
            currentResearchMaterials.clear();
            currentResearchMaterials.putAll(materialsMap);

            var unlockedTechsList = (List<String>) saveData.get("UnlockedTechs");
            UnlockedTechs.clear();
            UnlockedTechs.addAll(unlockedTechsList);
            // 打印读取结果作为测试
            FrostCraftBU.i().getLogger().info("已加载解锁科技: " + String.join(", ", unlockedTechsList));
            FrostCraftBU.i().getLogger().info("当前研究项目: " + currentResearchProject);
            FrostCraftBU.i().getLogger().info("当前研究材料: " + currentResearchMaterials);
        } catch (IOException e) {
            FrostCraftBU.i().getLogger().log(Level.SEVERE, "加载研究数据失败", e);
        }
        // 更新记分板分数
        resetAllScores();
        updateCurrentMaterialScores();

    }

    // Scoreboard related fields
    private String woodReqEntryName = "fc_wood_req"; // Default value
    private String steelReqEntryName = "fc_steel_req"; // Default value
    private String woodCurrentEntryName = "fc_wood_current"; // Default value
    private String steelCurrentEntryName = "fc_steel_current"; // Default value
    private String statusEntryName = "#ResearchStatus"; // Default value
    private String woodMaterialName = "木板"; // Default value
    private String steelMaterialName = "钢材"; // Default value

    public static TechTreeManager i() {
        if (instance == null) {
            instance = new TechTreeManager();
        }
        return instance;
    }

    public void loadConfig() {
        FileConfiguration config = FrostCraftBU.i().getConfig();
        woodReqEntryName = config.getString("ScoreboardObjectives.WoodReq", woodReqEntryName);
        steelReqEntryName = config.getString("ScoreboardObjectives.SteelReq", steelReqEntryName);
        woodCurrentEntryName = config.getString("ScoreboardObjectives.WoodCurrent", woodCurrentEntryName);
        steelCurrentEntryName = config.getString("ScoreboardObjectives.SteelCurrent", steelCurrentEntryName);
        statusEntryName = config.getString("ScoreboardObjectives.StatusObjName", statusEntryName);
        woodMaterialName = config.getString("ScoreboardObjectives.WoodMaterialName", woodMaterialName);
        steelMaterialName = config.getString("ScoreboardObjectives.SteelMaterialName", steelMaterialName);
        // log all name
        FrostCraftBU.i().getLogger().info("woodReqEntryName: " + woodReqEntryName);
        FrostCraftBU.i().getLogger().info("steelReqEntryName: " + steelReqEntryName);
        FrostCraftBU.i().getLogger().info("woodCurrentEntryName: " + woodCurrentEntryName);
        FrostCraftBU.i().getLogger().info("steelCurrentEntryName: " + steelCurrentEntryName);
        FrostCraftBU.i().getLogger().info("statusEntryName: " + statusEntryName);
        FrostCraftBU.i().getLogger().info("woodMaterialName: " + woodMaterialName);
        FrostCraftBU.i().getLogger().info("steelMaterialName: " + steelMaterialName);
        File file = new File(FrostCraftBU.i().getDataFolder(), "TechTree.yml");
        if (!file.exists()) {
            FrostCraftBU.i().saveResource("TechTree.yml", false);
        }
        YamlConfiguration techTreeConfig = YamlConfiguration.loadConfiguration(file);
        var techsSection = techTreeConfig.getConfigurationSection("techs");
        if (techsSection == null) {
            FrostCraftBU.i().getLogger().warning("TechTree.yml中未找到 'techs' 部分!");
            return;
        }
        techs.clear(); // 清空旧数据以重新加载
        techsSection.getKeys(false).forEach(name -> {
            var tech = techsSection.getConfigurationSection(name);
            if (tech != null) {
                var dependencies = tech.getStringList("dependencies");
                var unlockedBuildings = tech.getStringList("unlockedBuildings");
                var unlockedRecipes = tech.getStringList("unlockedRecipes");
                var unlockedGatherRecipes = tech.getStringList("unlockedGatherRecipes");
                var unlockedConsumeRecipes = tech.getStringList("unlockedConsumeRecipes");
                var consumeSection = tech.getConfigurationSection("consume");
                var consume = new HashMap<String, Integer>();
                if (consumeSection != null) {
                    consumeSection.getKeys(false).forEach(key -> {
                        consume.put(key, consumeSection.getInt(key));
                    });
                } else {
                    FrostCraftBU.i().getLogger().warning("科技 '" + name + "' 缺少 'consume' 部分!");
                }
                this.techs.put(name, new Tech(name, dependencies, unlockedBuildings, unlockedRecipes, unlockedGatherRecipes, unlockedConsumeRecipes, consume));
            }
        });
        FrostCraftBU.i().getLogger().info("已加载 " + this.techs.size() + " 个科技。");
        // TODO: Load unlocks and research progress from a file on server start/reload
        // 首次加载时，可能需要初始化记分板
        resetAllScores();

    }

    public Tech getTechByName(String techName) {
        return techs.get(techName);
    }

    // --- Research Logic ---

    /**
     * Sets the active research project and updates scoreboards.
     * 
     * @param techName The name of the tech to research. Set to null to stop
     *                 research.
     * @return true if the tech exists and was set, false otherwise.
     */
    public boolean setCurrentResearchProject(String techName) {
        if (techName != null && !techs.containsKey(techName)) {
            FrostCraftBU.i().getLogger().warning("尝试设置不存在的科技项目: " + techName);
            return false; // Tech doesn't exist
        }
        // Prevent setting already unlocked tech as current research
        if (techName != null && isUnlocked(techName)) {
            FrostCraftBU.i().getLogger().info("科技 '" + techName + "' 已解锁，无法设为当前研究。");
            // Optionally clear current research if it was this one
            if (techName.equals(this.currentResearchProject)) {
                this.currentResearchProject = null;
                resetAllScores(); // Reset scores if the already unlocked tech was the current one
            }
            return false;
        }

        this.currentResearchProject = techName;

        if (techName != null) {
            // Update tech status scoreboards
            Tech current = techs.get(techName);
            for (String tName : techs.keySet()) {
                // 如果科技已解锁，保持解锁状态(2)
                // 如果是当前研究项目，设置为研究中状态(1)
                // 否则设置为未选择状态(0)
                int status = isUnlocked(tName) ? TECH_STATUS_UNLOCKED
                        : tName.equals(techName) ? TECH_STATUS_RESEARCHING : TECH_STATUS_NONE;
                setScoreboardScore(tName, statusEntryName, status);
            }

            // Update material requirement scoreboards
            int woodReq = current.consume.getOrDefault(woodMaterialName, 0);
            int steelReq = current.consume.getOrDefault(steelMaterialName, 0);
            setScoreboardScore(woodReqEntryName, statusEntryName, woodReq);
            setScoreboardScore(steelReqEntryName, statusEntryName, steelReq);
            currentResearchMaterials.clear();
            // Reset current material scoreboards (use accumulated values)
            updateCurrentMaterialScores(); // 更新为当前已存储的材料数量
        } else {
            // Clear all research-related scores if stopping research
            resetAllScores();
        }

        // TODO: Save this change to a file
        return true;
    }

    public String getCurrentResearchProject() {
        return currentResearchProject;
    }

    public String getJustFinishResearchProject() {
        return justFinishResearchProject;
    }

    public Map<String, Integer> getResearchMaterials() {
        return new HashMap<>(currentResearchMaterials); // Return a copy
    }

    /**
     * Adds research materials, updates scoreboards, and checks for completion.
     * 
     * @param materialName The name of the material.
     * @param amount       The amount to add.
     * @return true if the addition completed the current research, false otherwise.
     */
    public boolean addResearchMaterial(String materialName, int amount) {
        if (amount <= 0) {
            return false;
        }
        if (currentResearchProject == null) {
            // 不能向未进行的研究添加材料
            FrostCraftBU.i().getLogger().fine("没有正在进行的研究项目，无法添加材料。");
            return false;
        }
        currentResearchMaterials.merge(materialName, amount, Integer::sum);

        // Update current material scoreboards
        updateCurrentMaterialScores();

        // TODO: Save material changes to a file
        return checkAndCompleteResearch();
    }

    private void updateCurrentMaterialScores() {
        if (currentResearchProject != null) {
            int currentWood = currentResearchMaterials.getOrDefault(woodMaterialName, 0);
            int currentSteel = currentResearchMaterials.getOrDefault(steelMaterialName, 0);
            setScoreboardScore(woodCurrentEntryName, statusEntryName, currentWood);
            // log
            FrostCraftBU.i().getLogger().info("woodCurrentEntryName: " + woodCurrentEntryName);
            FrostCraftBU.i().getLogger().info("statusEntryName: " + statusEntryName);
            FrostCraftBU.i().getLogger().info("currentWood: " + currentWood);
            setScoreboardScore(steelCurrentEntryName, statusEntryName, currentSteel);
        } else {
            // 如果没有研究项目，当前材料分数应为0
            setScoreboardScore(woodCurrentEntryName, statusEntryName, 0);
            setScoreboardScore(steelCurrentEntryName, statusEntryName, 0);
        }
    }

    /**
     * Checks if the current research project's requirements are met and completes
     * it if possible.
     * 
     * @return true if the research was completed, false otherwise.
     */
    private boolean checkAndCompleteResearch() {
        if (currentResearchProject == null || !techs.containsKey(currentResearchProject)
                || isUnlocked(currentResearchProject)) { // Don't complete if already unlocked
            return false;
        }

        Tech tech = techs.get(currentResearchProject);
        Map<String, Integer> required = tech.consume;

        // Check dependencies
        for (String dependency : tech.dependencies) {
            if (!isUnlocked(dependency)) {
                FrostCraftBU.i().getLogger()
                        .fine("科技 '" + currentResearchProject + "' 的前置科技 '" + dependency + "' 未解锁。");
                return false;
            }
        }

        // Check if enough materials are available
        for (Map.Entry<String, Integer> entry : required.entrySet()) {
            if (currentResearchMaterials.getOrDefault(entry.getKey(), 0) < entry.getValue()) {
                // FrostCraftBU.i().getLogger().finest("材料不足: 需要 " + entry.getValue() + " " +
                // entry.getKey() + ", 当前有 " +
                // currentResearchMaterials.getOrDefault(entry.getKey(), 0));
                return false; // Not enough materials
            }
        }

        // Consume materials
        for (Map.Entry<String, Integer> entry : required.entrySet()) {
            currentResearchMaterials.compute(entry.getKey(),
                    (key, currentAmount) -> (currentAmount == null ? 0 : currentAmount) - entry.getValue());
            // Optional: Remove material if amount becomes zero or less
            currentResearchMaterials.remove(entry.getKey(), 0);
        }

        // Unlock the tech (add to global list)
        UnlockedTechs.add(currentResearchProject);
        // 更新记分板状态为已解锁(2)
        setScoreboardScore(currentResearchProject, statusEntryName, TECH_STATUS_UNLOCKED);
        String completedTech = currentResearchProject;
        currentResearchProject = null; // Clear current research project
        currentResearchMaterials.clear(); // 清空材料池

        // 重置其他记分板分数
        resetAllScores();

        justFinishResearchProject = completedTech;
        Bukkit.getScheduler().runTaskLater(FrostCraftBU.i(), () -> {
            justFinishResearchProject = null;
        }, 100);

        // TODO: Save state changes (unlocks, materials, current project) to a file
        FrostCraftBU.i().getLogger().info("研究完成: " + completedTech); // Log completion
        // TODO: Announce completion or trigger events

        return true;
    }

    /**
     * Resets all research-related scoreboard scores to appropriate values.
     * Sets unlocked techs to TECH_STATUS_UNLOCKED (2).
     */
    private void resetAllScores() {
        // Reset tech status scores
        for (String techName : techs.keySet()) {
            // 如果科技已解锁，设置为已解锁状态(2)，否则设置为未选择状态(0)
            int status = isUnlocked(techName) ? TECH_STATUS_UNLOCKED : TECH_STATUS_NONE;
            setScoreboardScore(techName, statusEntryName, status);
        }
        // Reset material scores
        setScoreboardScore(woodReqEntryName, statusEntryName, 0);
        setScoreboardScore(steelReqEntryName, statusEntryName, 0);
        setScoreboardScore(woodCurrentEntryName, statusEntryName, 0);
        setScoreboardScore(steelCurrentEntryName, statusEntryName, 0);
    }

    /**
     * Helper method to set a score on the main scoreboard.
     * Assumes the objective exists. Logs a warning if not found.
     *
     * @param entry         The entry (e.g., player name or status holder).
     * @param objectiveName The name of the objective.
     * @param value         The score value to set.
     */
    private void setScoreboardScore(String entry, String objectiveName, int value) {
        try {
            Scoreboard scoreboard = Bukkit.getScoreboardManager().getMainScoreboard();
            Objective objective = scoreboard.getObjective(objectiveName);
            if (objective != null) {
                Score score = objective.getScore(entry);
                score.setScore(value);
            } else {
                // 重要提示: 只有在配置了对应名称的记分项时才会生效
                // 可以考虑在插件启动时检查或创建这些记分项
                FrostCraftBU.i().getLogger().warning("记分板目标 '" + objectiveName + "' 不存在！请在游戏中创建它。");
            }
        } catch (Exception e) {
            FrostCraftBU.i().getLogger().log(Level.SEVERE, "设置记分板分数时出错 (" + objectiveName + "): ", e);
        }
    }

    /**
     * Forces a tech to be unlocked, bypassing material and dependency
     * checks. (For testing/admin)
     */
    public boolean forceUnlock(String techName) {
        if (!techs.containsKey(techName)) {
            return false; // Tech doesn't exist
        }
        if (UnlockedTechs.contains(techName)) {
            return false; // Already unlocked
        }
        UnlockedTechs.add(techName);
        // 更新记分板状态为已解锁(2)
        setScoreboardScore(techName, statusEntryName, TECH_STATUS_UNLOCKED);
        // TODO: Save state changes to a file
        saveResearchData(); // 保存研究数据
        return true;
    }

    /**
     * Checks if a tech is unlocked .
     * 
     * @param techName The name of the tech.
     * @return true if the tech is unlocked, false otherwise.
     */
    public boolean isUnlocked(String techName) {
        return UnlockedTechs.contains(techName);
    }

    public List<String> getUnlockedTechs() {
        return new ArrayList<>(UnlockedTechs); // Return a copy
    }

    public List<String> getUnlockedBuildings() {
        return UnlockedTechs.stream().flatMap(techName -> {
            if (!techs.containsKey(techName)) {
                FrostCraftBU.i().getLogger().warning("科技 '" + techName + "' 不存在！");
                return Stream.empty();
            }
            return techs.get(techName).unlockedBuildings.stream();
        })
                .distinct().collect(Collectors.toList());
    }

    public List<String> getUnlockedRecipes() {
        return UnlockedTechs.stream().flatMap(techName -> {
            if (!techs.containsKey(techName)) {
                FrostCraftBU.i().getLogger().warning("科技 '" + techName + "' 不存在！");
                return Stream.empty();
            }
            return techs.get(techName).unlockedRecipes.stream();
        })
                .distinct().collect(Collectors.toList());
    }

    public List<String> getUnlockedGatherRecipes() {
        return UnlockedTechs.stream().flatMap(techName -> {
            if (!techs.containsKey(techName)) {
                FrostCraftBU.i().getLogger().warning("科技 '" + techName + "' 不存在！");
                return Stream.empty();
            }
            return techs.get(techName).unlockedGatherRecipes.stream();
        })
                .distinct().collect(Collectors.toList());
    }

    public List<String> getUnlockedConsumeRecipes() {
        return UnlockedTechs.stream().flatMap(techName -> {
            if (!techs.containsKey(techName)) {
                FrostCraftBU.i().getLogger().warning("科技 '" + techName + "' 不存在！");
                return Stream.empty();
            }
            return techs.get(techName).unlockedConsumeRecipes.stream();
        })
                .distinct().collect(Collectors.toList());
    }


    public boolean isBuildingUnlocked(String buildingName) {
        return getUnlockedBuildings().contains(buildingName);
    }

    /**
     * 重新锁定指定科技
     * 
     * @param techName 要锁定的科技名称
     * @return true如果成功锁定，false如果科技不存在或未被解锁
     */
    public boolean lockTech(String techName) {
        if (!techs.containsKey(techName)) {
            FrostCraftBU.i().getLogger().warning("尝试锁定不存在的科技: " + techName);
            return false;
        }

        if (!UnlockedTechs.contains(techName)) {
            return false; // 科技未被解锁，无需锁定
        }

        // 如果是当前研究项目，取消研究
        if (techName.equals(currentResearchProject)) {
            currentResearchProject = null;
        }

        // 从已解锁列表中移除
        UnlockedTechs.remove(techName);

        // 更新记分板状态为未选择(0)
        setScoreboardScore(techName, statusEntryName, TECH_STATUS_NONE);

        // 保存研究数据
        saveResearchData();

        return true;
    }

    /**
     * 重新锁定所有已解锁的科技
     * 
     * @return 成功锁定的科技数量
     */
    public int lockAllTechs() {
        if (UnlockedTechs.isEmpty()) {
            return 0;
        }

        int count = UnlockedTechs.size();

        // 清空当前研究项目
        currentResearchProject = null;

        // 清空当前研究材料
        currentResearchMaterials.clear();

        // 清空已解锁科技列表
        UnlockedTechs.clear();

        // 重置所有记分板分数
        resetAllScores();

        // 保存研究数据
        saveResearchData();

        return count;
    }

    public static class Tech {
        String name;
        List<String> dependencies;
        List<String> unlockedBuildings;
        List<String> unlockedRecipes;
        List<String> unlockedGatherRecipes;
        List<String> unlockedConsumeRecipes;
        Map<String, Integer> consume;

        public String getName() {
            return name;
        }

        public List<String> getDependencies() {
            return dependencies;
        }

        public List<String> getUnlockedBuildings() {
            return unlockedBuildings;
        }

        public List<String> getUnlockedRecipes() {
            return unlockedRecipes;
        }

        public List<String> getUnlockedGatherRecipes() {
            return unlockedGatherRecipes;
        }

        public List<String> getUnlockedConsumeRecipes() {
            return unlockedConsumeRecipes;
        }

        public Map<String, Integer> getConsume() {
            return consume;
        }

        public Tech(String name, List<String> dependencies, List<String> unlockedBuildings, List<String> unlockedRecipes,
                List<String> unlockedGatherRecipes, List<String> unlockedConsumeRecipes, Map<String, Integer> consume) {
            this.name = name;
            this.dependencies = dependencies;
            this.unlockedBuildings = unlockedBuildings;
            this.unlockedRecipes = unlockedRecipes;
            this.unlockedGatherRecipes = unlockedGatherRecipes;
            this.unlockedConsumeRecipes = unlockedConsumeRecipes;
            this.consume = consume;
        }
    }

}
