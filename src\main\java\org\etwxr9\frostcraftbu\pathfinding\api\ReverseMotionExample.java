package org.etwxr9.frostcraftbu.pathfinding.api;

import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.util.Vector;
import org.etwxr9.frostcraftbu.FrostCraftBU;
import java.util.List;
import java.util.Optional;

/**
 * 反向运动示例
 * 
 * 展示如何使用反向运动功能实现往返巡逻、双向传送等效果
 */
public class ReverseMotionExample {
    
    /**
     * 往返巡逻任务
     * 无人机在路径上往返运动，到达终点后自动反向
     */
    public static class PatrolTask extends BukkitRunnable {
        
        private final List<Location> path;
        private final double speed;
        private final PathSmoothingUtil.SmoothingType smoothingType;
        private final Player player;
        private final PatrolCallback callback;
        
        private int elapsedTicks = 0;
        private boolean reverse = false; // 当前运动方向
        private int cycleCount = 0; // 完成的循环次数
        
        public PatrolTask(List<Location> path, double speed, 
                         PathSmoothingUtil.SmoothingType smoothingType,
                         Player player, PatrolCallback callback) {
            this.path = path;
            this.speed = speed;
            this.smoothingType = smoothingType;
            this.player = player;
            this.callback = callback;
        }
        
        @Override
        public void run() {
            // 检查路径是否完成
            if (PathSmoothingUtil.isPathCompleted(elapsedTicks, speed, path, reverse)) {
                // 到达终点，切换方向
                reverse = !reverse;
                elapsedTicks = 0;
                cycleCount++;
                
                if (callback != null) {
                    callback.onDirectionChanged(reverse, cycleCount);
                }
                
                // 如果需要限制循环次数，可以在这里检查
                // if (cycleCount >= maxCycles) { cancel(); return; }
            }
            
            // 计算当前位置
            Optional<Location> currentPosition = PathSmoothingUtil.calculateRealtimePosition(
                elapsedTicks, speed, path, smoothingType, 10, reverse);
            
            if (!currentPosition.isPresent()) {
                cancel();
                return;
            }
            
            // 计算运动方向
            Optional<Vector> direction = PathSmoothingUtil.calculateMovementDirection(
                elapsedTicks, speed, path, smoothingType, reverse);
            
            // 计算进度
            double progress = PathSmoothingUtil.calculatePathProgress(elapsedTicks, speed, path, reverse);
            
            // 回调处理位置更新
            if (callback != null) {
                callback.onPositionUpdate(currentPosition.get(), direction.get(), progress, reverse, cycleCount);
            }
            
            elapsedTicks++;
        }
        
        /**
         * 获取当前运动方向
         */
        public boolean isReverse() {
            return reverse;
        }
        
        /**
         * 获取完成的循环次数
         */
        public int getCycleCount() {
            return cycleCount;
        }
        
        /**
         * 强制切换方向
         */
        public void switchDirection() {
            reverse = !reverse;
            elapsedTicks = 0;
            cycleCount++;
        }
    }
    
    /**
     * 巡逻回调接口
     */
    public interface PatrolCallback {
        /**
         * 位置更新回调
         */
        void onPositionUpdate(Location position, Vector direction, 
                            double progress, boolean reverse, int cycleCount);
        
        /**
         * 方向改变回调
         */
        void onDirectionChanged(boolean newReverse, int cycleCount);
    }
    
    /**
     * 创建往返巡逻任务
     */
    public static PatrolTask createPatrolTask(List<Location> path, double speed,
                                            PathSmoothingUtil.SmoothingType smoothingType,
                                            Player player, PatrolCallback callback) {
        PatrolTask task = new PatrolTask(path, speed, smoothingType, player, callback);
        task.runTaskTimer(FrostCraftBU.i(), 0L, 1L);
        return task;
    }
    
    /**
     * 示例：创建简单的巡逻无人机
     */
    public static void createSimplePatrolDrone(Player player, List<Location> path) {
        double speed = 3.0; // 3米/秒
        PathSmoothingUtil.SmoothingType smoothing = PathSmoothingUtil.SmoothingType.CATMULL_ROM;
        
        PatrolCallback callback = new PatrolCallback() {
            @Override
            public void onPositionUpdate(Location position, Vector direction, 
                                       double progress, boolean reverse, int cycleCount) {
                // 每20tick显示一次状态
                if (player.getTicksLived() % 20 == 0) {
                    String directionText = reverse ? "返回" : "前进";
                    player.sendMessage(String.format("§7巡逻状态: §a%s §7进度: §f%.1f%% §7循环: §f%d", 
                        directionText, progress * 100, cycleCount));
                }
                
                // 显示粒子效果
                if (player.getTicksLived() % 5 == 0) {
                    // 根据方向使用不同颜色的粒子
                    // 正向：绿色，反向：红色
                    // player.getWorld().spawnParticle(
                    //     reverse ? Particle.REDSTONE : Particle.VILLAGER_HAPPY, 
                    //     position, 1);
                }
            }
            
            @Override
            public void onDirectionChanged(boolean newReverse, int cycleCount) {
                String directionText = newReverse ? "开始返回" : "开始前进";
                player.sendMessage("§6" + directionText + " §7(第" + cycleCount + "次循环)");
            }
        };
        
        PatrolTask task = createPatrolTask(path, speed, smoothing, player, callback);
        
        player.sendMessage("§a巡逻无人机已启动！");
        player.sendMessage("§7速度: " + speed + " 米/秒");
        player.sendMessage("§7平滑算法: " + smoothing.name());
        player.sendMessage("§7使用 §e/frostcraftbu dronetest §7来停止");
    }
    
    /**
     * 示例：创建双向传送带效果
     */
    public static void createConveyorBeltEffect(Player player, List<Location> path, 
                                              List<org.bukkit.entity.Item> items) {
        double speed = 2.0; // 2米/秒
        PathSmoothingUtil.SmoothingType smoothing = PathSmoothingUtil.SmoothingType.LINEAR;
        
        // 为每个物品创建独立的运动任务
        for (int i = 0; i < items.size(); i++) {
            org.bukkit.entity.Item item = items.get(i);
            boolean reverse = i % 2 == 1; // 奇数索引的物品反向运动
            int startDelay = i * 10; // 每个物品延迟10tick启动
            
            new BukkitRunnable() {
                private int elapsedTicks = 0;
                
                @Override
                public void run() {
                    if (!item.isValid()) {
                        cancel();
                        return;
                    }
                    
                    // 检查是否完成路径
                    if (PathSmoothingUtil.isPathCompleted(elapsedTicks, speed, path, reverse)) {
                        // 传送带上的物品到达终点后移除
                        item.remove();
                        cancel();
                        return;
                    }
                    
                    // 计算位置
                    Optional<Location> position = PathSmoothingUtil.calculateRealtimePosition(
                        elapsedTicks, speed, path, smoothing, 10, reverse);
                    
                    if (position.isPresent()) {
                        item.teleport(position.get());
                    }
                    
                    elapsedTicks++;
                }
            }.runTaskTimer(FrostCraftBU.i(), startDelay, 1L);
        }
        
        player.sendMessage("§a双向传送带效果已启动！");
        player.sendMessage("§7" + items.size() + " 个物品正在传送");
    }
    
    /**
     * 示例：创建时间倒流效果
     */
    public static void createTimeRewindEffect(Player player, List<Location> recordedPath) {
        double speed = 8.0; // 快速回放
        PathSmoothingUtil.SmoothingType smoothing = PathSmoothingUtil.SmoothingType.CATMULL_ROM;
        
        // 先正向播放，然后反向播放
        new BukkitRunnable() {
            private int elapsedTicks = 0;
            private boolean reverse = false;
            private boolean firstPhaseComplete = false;
            
            @Override
            public void run() {
                // 检查当前阶段是否完成
                if (PathSmoothingUtil.isPathCompleted(elapsedTicks, speed, recordedPath, reverse)) {
                    if (!firstPhaseComplete) {
                        // 第一阶段完成，开始反向播放
                        firstPhaseComplete = true;
                        reverse = true;
                        elapsedTicks = 0;
                        player.sendMessage("§6开始时间倒流...");
                    } else {
                        // 第二阶段完成，结束效果
                        player.sendMessage("§a时间倒流效果完成！");
                        cancel();
                        return;
                    }
                }
                
                // 计算位置
                Optional<Location> position = PathSmoothingUtil.calculateRealtimePosition(
                    elapsedTicks, speed, recordedPath, smoothing, 10, reverse);
                
                if (position.isPresent()) {
                    // 在这里可以生成特殊的粒子效果
                    // 正向：普通粒子，反向：特殊的时间倒流粒子
                    // Particle particleType = reverse ? Particle.PORTAL : Particle.ENCHANTMENT_TABLE;
                    // player.getWorld().spawnParticle(particleType, position, 5);
                    
                    // 显示进度
                    if (elapsedTicks % 10 == 0) {
                        double progress = PathSmoothingUtil.calculatePathProgress(elapsedTicks, speed, recordedPath, reverse);
                        String phase = reverse ? "倒流" : "正放";
                        player.sendMessage(String.format("§7%s进度: §f%.1f%%", phase, progress * 100));
                    }
                }
                
                elapsedTicks++;
            }
        }.runTaskTimer(FrostCraftBU.i(), 0L, 1L);
        
        player.sendMessage("§6时间倒流效果已启动！");
        player.sendMessage("§7将先正向播放，然后反向播放");
    }
    
    /**
     * 工具方法：计算往返一次的总时间
     */
    public static int calculateRoundTripDuration(List<Location> path, double speed) {
        int oneWayDuration = PathSmoothingUtil.calculatePathDurationTicks(speed, path);
        return oneWayDuration * 2; // 往返需要两倍时间
    }
}
