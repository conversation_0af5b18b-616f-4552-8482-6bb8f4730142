package org.etwxr9.frostcraftbu.Listener;

import java.util.Map;

import org.bukkit.Location;
import org.bukkit.block.Sign;
import org.bukkit.block.sign.Side;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.event.inventory.InventoryAction;
import org.bukkit.event.player.PlayerInteractEvent;
import org.etwxr9.frostcraftbu.FrostCraftBU;
import org.etwxr9.frostcraftbu.Building.BuildingManager;
import org.etwxr9.frostcraftbu.Item.ItemManager;
import org.etwxr9.frostcraftbu.Module.Behavior.IProduction;
import org.etwxr9.frostcraftbu.Module.Behavior.IResourceGathering;
import org.etwxr9.frostcraftbu.Recipe.RecipeManager;
import org.ipvp.canvas.slot.ClickOptions;
import org.ipvp.canvas.type.ChestMenu;

import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;

public class GatherSignListener implements Listener {

    @EventHandler
    public void onRClickSign(PlayerInteractEvent e) {
        if (e.getAction() != Action.RIGHT_CLICK_BLOCK)
            return;

        var signBlock = e.getClickedBlock();
        if (!(signBlock.getState() instanceof Sign))
            return;

        var signState = (Sign) signBlock.getState();
        String[] signData = signState.getSide(Side.FRONT).getLines();

        // 检查牌子第一行是否符合配置
        if (!signData[0].equals(FrostCraftBU.i().getConfig().getString("GatherSign0")))
            return;

        e.setCancelled(true);
        Player p = e.getPlayer();

        // 获取建筑
        Location buildingLocation = signBlock.getLocation();
        var building = BuildingManager.i().getBuildingAt(buildingLocation);
        if (building == null) {
            p.sendMessage("该牌子不在任何建筑内");
            return;
        }

        // log
        FrostCraftBU.i().getLogger().info("GatherSign: " + building.getModules().keySet().toString());

        // 检查建筑是否有采集模块
        var gatherModule = building.getModules().values().stream()
                .filter(module -> module instanceof IResourceGathering)
                .map(module -> (IResourceGathering) module)
                .findFirst();

        if (gatherModule.isEmpty()) {
            p.sendMessage("该建筑没有采集模块");
            return;
        }

        // 创建产线选择菜单
        var lineMenu = ChestMenu.builder(6)
                .title("选择生产线")
                .build();

        // 获取所有产线的状态
        for (int lineIndex = 0; lineIndex < gatherModule.get().getLineCount(); lineIndex++) {
            final int currentLineIndex = lineIndex;
            String currentRecipe = gatherModule.get().getRecipe(lineIndex);

            if (currentRecipe == null) {
                // 如果产线没有配方，使用默认按钮
                var btnItem = ItemManager.getItem("production_line_default");
                lineMenu.getSlot(lineIndex).setItem(btnItem);
            } else {
                // 如果产线有配方，使用配方对应的物品
                var btnItem = ItemManager.getItem(RecipeManager.i().getGatherRecipe(currentRecipe).getName());
                lineMenu.getSlot(lineIndex).setItem(btnItem);
            }

            // 设置点击处理
            lineMenu.getSlot(lineIndex).setClickOptions(
                    ClickOptions.builder().allow(ClickType.LEFT).allow(InventoryAction.PICKUP_ONE).build());

            lineMenu.getSlot(lineIndex).setClickHandler((player, info) -> {
                lineMenu.close(player);
                openRecipeMenu(player, gatherModule.get(), currentLineIndex);
            });
        }

        lineMenu.open(p);
    }

    private void openRecipeMenu(Player player, IResourceGathering resourceGathingModule, int lineIndex) {
        var recipeMenu = ChestMenu.builder(6)
                .title("选择配方")
                .build();

        // 添加清除配方按钮
        var clearItem = ItemManager.getItem("clear_recipe");
        recipeMenu.getSlot(0).setItem(clearItem);
        recipeMenu.getSlot(0).setClickOptions(
                ClickOptions.builder().allow(ClickType.LEFT).allow(InventoryAction.PICKUP_ONE).build());
        recipeMenu.getSlot(0).setClickHandler((p, info) -> {
            resourceGathingModule.setRecipe(lineIndex, null);
            recipeMenu.close(p);
            p.sendMessage("已清除配方");
        });

        // 获取可用配方列表并创建按钮
        int slotIndex = 1;
        for (String recipeId : resourceGathingModule.getAvailableRecipes(lineIndex)) {
            var btnItem = ItemManager.getItem(RecipeManager.i().getGatherRecipe(recipeId).getName());
            // 读取并添加需求lore，格式： Ax1 + Bx2 -> Cx3
            var recipe = RecipeManager.i().getGatherRecipe(recipeId);
            String formula = "";
            for (Map.Entry<String, Integer> entry : recipe.getOutput().entrySet()) {
                formula += entry.getKey() + "x" + entry.getValue() + " + ";
            }
            formula = formula.substring(0, formula.length() - 3);
            var lore = btnItem.lore();
            lore.add(Component.text(formula).color(NamedTextColor.WHITE));
            // 耗时
            lore.add(Component.text("耗时: " + String.format("%.2f", (double) recipe.getTime() / 20) + "秒")
                    .color(NamedTextColor.WHITE));
            btnItem.lore(lore);
            recipeMenu.getSlot(slotIndex).setItem(btnItem);
            recipeMenu.getSlot(slotIndex).setClickOptions(
                    ClickOptions.builder().allow(ClickType.LEFT).allow(InventoryAction.PICKUP_ONE).build());

            final String finalRecipeId = recipeId;
            recipeMenu.getSlot(slotIndex).setClickHandler((p, info) -> {
                if (resourceGathingModule.setRecipe(lineIndex, finalRecipeId)) {
                    p.sendMessage("配方设置成功");
                } else {
                    p.sendMessage("配方设置失败");
                }
                recipeMenu.close(p);
            });

            slotIndex++;
        }

        recipeMenu.open(player);
    }
}
