package org.etwxr9.frostcraftbu.Module.Behavior;

import java.util.List;

public interface IResourceConsumption {
    void tick();
    int getLineCount();
    boolean setRecipe(int lineIndex, String recipeId);
    String getRecipe(int lineIndex);
    int getProgress(int lineIndex);
    boolean canProduce(int lineIndex);
    List<String> getAvailableRecipes(int lineIndex);
    /**
     * 设置消耗回调函数
     */
    void consumeCallback(String recipeId);
}