package org.etwxr9.frostcraftbu.pathfinding.api;

import org.bukkit.Location;
import java.util.List;

/**
 * 无人机寻路结果封装类
 * 
 * 包含寻路的完整结果信息，包括原始路径、平滑路径、寻路状态等
 */
public class DronePathResult {

    /**
     * 寻路状态枚举
     */
    public enum PathState {
        SUCCESS, // 寻路成功
        FALLBACK, // 使用备用策略成功
        FAILED, // 寻路失败
        NO_PATH, // 无法找到路径
        INVALID_INPUT // 输入参数无效
    }

    private final PathState state;
    private final Location startLocation;
    private final Location endLocation;
    private final List<Location> rawPath;
    private final double pathLength;
    private final long computeTime;
    private final String errorMessage;

    /**
     * 构造成功的寻路结果
     */
    public DronePathResult(PathState state, List<Location> rawPath,
            double pathLength, long computeTime, Location startLocation, Location endLocation) {
        this.state = state;
        this.rawPath = rawPath;
        this.pathLength = pathLength;
        this.computeTime = computeTime;
        this.errorMessage = null;
        this.startLocation = startLocation;
        this.endLocation = endLocation;
    }

    /**
     * 构造失败的寻路结果
     */
    public DronePathResult(PathState state, String errorMessage, long computeTime) {
        this.state = state;
        this.rawPath = null;
        this.pathLength = 0;
        this.computeTime = computeTime;
        this.errorMessage = errorMessage;
        this.startLocation = null;
        this.endLocation = null;
    }

    /**
     * 获取寻路状态
     */
    public PathState getState() {
        return state;
    }

    public Location getStartLocation() {
        return startLocation;
    }

    public Location getEndLocation() {
        return endLocation;
    }

    /**
     * 获取原始路径（未平滑）
     * 
     * @return 原始路径点列表，如果寻路失败则返回null
     */
    public List<Location> getRawPath() {
        return rawPath;
    }



    /**
     * 获取路径长度（欧几里得距离）
     */
    public double getPathLength() {
        return pathLength;
    }

    /**
     * 获取计算耗时（毫秒）
     */
    public long getComputeTime() {
        return computeTime;
    }

    /**
     * 获取错误信息
     */
    public String getErrorMessage() {
        return errorMessage;
    }

    /**
     * 检查寻路是否成功
     */
    public boolean isSuccessful() {
        return state == PathState.SUCCESS || state == PathState.FALLBACK;
    }

    /**
     * 检查是否使用了备用策略
     */
    public boolean hasFallback() {
        return state == PathState.FALLBACK;
    }

    /**
     * 获取推荐使用的路径（返回原始路径）
     */
    public List<Location> getRecommendedPath() {
        return rawPath;
    }

    /**
     * 获取路径点数量
     */
    public int getPathSize() {
        List<Location> path = getRecommendedPath();
        return path != null ? path.size() : 0;
    }

    @Override
    public String toString() {
        return String.format("DronePathResult{state=%s, pathSize=%d, pathLength=%.2f, computeTime=%dms%s}",
                state, getPathSize(), pathLength, computeTime,
                errorMessage != null ? ", error='" + errorMessage + "'" : "");
    }
}
