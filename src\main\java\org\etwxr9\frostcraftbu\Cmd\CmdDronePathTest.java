package org.etwxr9.frostcraftbu.Cmd;

import org.bukkit.Location;
import org.bukkit.entity.ItemDisplay;
import org.bukkit.entity.Player;
import org.etwxr9.frostcraftbu.FrostCraftBU;
import org.etwxr9.frostcraftbu.Manager.DroneManager;
import org.etwxr9.frostcraftbu.pathfinding.api.DronePathfindingService;

import java.util.List;

/**
 * 无人机寻路API测试命令
 *
 * 用法：/frostcraftbu dronetest [subcommand] [args...]
 * 子命令：
 * - path [distance] [smoothing] - 测试寻路功能
 * - realtime [distance] [speed] [smoothing] - 测试实时运动功能
 * - distance: 测试距离（默认50格）
 * - speed: 运动速度（米/秒，默认5.0）
 * - smoothing: 平滑类型 linear/catmull/bezier（默认catmull）
 */
public class CmdDronePathTest implements SubCmdBase {

    @Override
    public boolean onSubCommand(Player player, String[] args) {

        // 检查寻路服务
        DronePathfindingService service = FrostCraftBU.getDronePathfindingService();
        if (service == null) {
            player.sendMessage("§c无人机寻路服务未初始化！");
            return false;
        }

        // 检查子命令
        if (args.length == 0) {
            sendHelp(player);
            return true;
        }

        String subCommand = args[0].toLowerCase();
        switch (subCommand) {
            case "path":
                return handlePathTest(player, service, args);
            case "remove":
                return handleRemoveTest(player, service, args);
            default:
                sendHelp(player);
                return true;
        }
    }

    private boolean handleRemoveTest(Player player, DronePathfindingService service, String[] args) {
        player.getLocation().getNearbyEntities(1, 1, 1).forEach(e -> {
            if (e instanceof ItemDisplay) {
                DroneManager.i().froceRemoveDrone((ItemDisplay) e);
            }
        });
        return true;
    }

    /**
     * 显示帮助信息
     */
    private void sendHelp(Player player) {
        player.sendMessage("§6=== 无人机寻路测试命令 ===");
        player.sendMessage("§e/frostcraftbu dronetest path [distance]");
        player.sendMessage("§7  - 测试寻路功能和路径可视化");
        player.sendMessage("§e/frostcraftbu dronetest remove");
        player.sendMessage("§7  - 移除所有测试无人机");
        player.sendMessage("§7参数说明：");
        player.sendMessage("§7  distance: 测试距离（10-200格，默认50）");
    }

    /**
     * 处理路径测试
     */
    private boolean handlePathTest(Player player, DronePathfindingService service, String[] args) {
        // 解析参数
        int distance = 50;

        if (args.length > 1) {
            try {
                distance = Integer.parseInt(args[1]);
                if (distance < 10 || distance > 200) {
                    player.sendMessage("§c距离必须在10-200格之间！");
                    return false;
                }
            } catch (NumberFormatException e) {
                player.sendMessage("§c无效的距离数值！");
                return false;
            }
        }

        // 执行路径测试
        return executePathTest(player, service, distance);
    }



    /**
     * 执行路径测试
     */
    private boolean executePathTest(Player player, DronePathfindingService service, int distance) {
        // 生成测试起点和终点
        Location playerLoc = player.getLocation();
        Location start = playerLoc.clone().add(0, 5, 0); // 玩家上方5格
        Location end = playerLoc.clone().add(distance, 10, distance / 2); // 对角线方向

        player.sendMessage("§6=== 无人机寻路API测试 ===");
        player.sendMessage("§7测试距离: §f" + distance + " 格");
        player.sendMessage("§7起点: §f" + formatLocation(start));
        player.sendMessage("§7终点: §f" + formatLocation(end));
        player.sendMessage("§6开始寻路测试...");

        long testStartTime = System.currentTimeMillis();

        // 执行寻路测试
        service.findPathAsync(start, end)
                .thenAccept(result -> {
                    long totalTime = System.currentTimeMillis() - testStartTime;

                    player.sendMessage("§6=== 寻路测试结果 ===");
                    player.sendMessage("§7状态: §f" + result.getState().name());
                    player.sendMessage("§7总耗时: §f" + totalTime + "ms");
                    player.sendMessage("§7寻路耗时: §f" + result.getComputeTime() + "ms");

                    if (result.isSuccessful()) {
                        player.sendMessage("§a✓ 寻路成功！");

                        List<Location> rawPath = result.getRawPath();

                        player.sendMessage("§7原始路径: §f" + rawPath.size() + " 个节点");

                        player.sendMessage("§7路径长度: §f" + String.format("%.2f", result.getPathLength()) + " 格");
                        player.sendMessage("§7直线距离: §f" + String.format("%.2f", start.distance(end)) + " 格");

                        double efficiency = (start.distance(end) / result.getPathLength()) * 100;
                        player.sendMessage("§7路径效率: §f" + String.format("%.1f", efficiency) + "%");

                        if (result.hasFallback()) {
                            player.sendMessage("§e⚠ 使用了备用寻路策略");
                        }

                        // 可视化路径
                        visualizePath(rawPath, player);

                    } else {
                        player.sendMessage("§c✗ 寻路失败");
                        if (result.getErrorMessage() != null) {
                            player.sendMessage("§c错误: " + result.getErrorMessage());
                        }
                    }

                    player.sendMessage("§6=== 测试完成 ===");
                })
                .exceptionally(throwable -> {
                    long totalTime = System.currentTimeMillis() - testStartTime;
                    player.sendMessage("§c=== 寻路测试异常 ===");
                    player.sendMessage("§c总耗时: " + totalTime + "ms");
                    player.sendMessage("§c异常: " + throwable.getMessage());
                    throwable.printStackTrace();
                    return null;
                });

        return true;
    }



    /**
     * 可视化路径
     */
    private void visualizePath(List<Location> rawPath, Player player) {
        // 显示原始路径（红色玻璃）
        for (Location loc : rawPath) {
            player.sendBlockChange(loc, org.bukkit.Material.RED_STAINED_GLASS.createBlockData());
        }

        player.sendMessage("§a路径已可视化：§c红色=原始路径");
        player.sendMessage("§7路径将在10秒后自动清除");

        // 10秒后清除可视化
        FrostCraftBU.i().getServer().getScheduler().runTaskLater(FrostCraftBU.i(), () -> {
            // 恢复原始方块
            for (Location loc : rawPath) {
                player.sendBlockChange(loc, loc.getBlock().getBlockData());
            }

            player.sendMessage("§7路径可视化已清除");
        }, 200L); // 10秒 = 200 ticks
    }

    /**
     * 格式化位置信息
     */
    private String formatLocation(Location location) {
        return String.format("(%.1f, %.1f, %.1f)",
                location.getX(), location.getY(), location.getZ());
    }

    @Override
    public List<String> onTabComplete(Player player, String[] args) {
        if (args.length == 1) {
            return List.of("path", "realtime");
        } else if (args.length == 2) {
            // 距离选项
            return List.of("10", "25", "50", "100", "150");
        } else if (args.length == 3) {
            String subCommand = args[0].toLowerCase();
            if ("path".equals(subCommand)) {
                // path命令的第三个参数是平滑类型
                return List.of("linear", "catmull", "bezier");
            } else if ("realtime".equals(subCommand)) {
                // realtime命令的第三个参数是速度
                return List.of("1.0", "2.5", "5.0", "7.5", "10.0");
            }
        } else if (args.length == 4) {
            String subCommand = args[0].toLowerCase();
            if ("path".equals(subCommand)) {
                // path命令的第四个参数是预处理
                return List.of("true", "false");
            } else if ("realtime".equals(subCommand)) {
                // realtime命令的第四个参数是平滑类型
                return List.of("linear", "catmull", "bezier");
            }
        } else if (args.length == 5 && "realtime".equals(args[0].toLowerCase())) {
            // realtime命令的第五个参数是反向运动
            return List.of("false", "true");
        } else if (args.length == 6 && "realtime".equals(args[0].toLowerCase())) {
            // realtime命令的第六个参数是预处理
            return List.of("true", "false");
        }
        return null;
    }
}
