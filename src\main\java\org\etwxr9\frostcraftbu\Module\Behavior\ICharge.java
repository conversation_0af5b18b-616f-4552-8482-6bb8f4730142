package org.etwxr9.frostcraftbu.Module.Behavior;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.bukkit.Location;
import org.bukkit.entity.ArmorStand;
import org.etwxr9.frostcraftbu.Module.BaseModuleSaveData;
import org.etwxr9.frostcraftbu.Module.ContainerModule.ContainerUnit;
import org.etwxr9.frostcraftbu.Module.ModuleManager;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * 充电建筑
 * 提供检索建筑内的一个充电桩实体（盔甲架）的方法
 */
public interface ICharge {

    Map<String, Location> getChargePoints();

    boolean hasAvailableChargePoint();

    Location getAvailableChargePoint();

    Location useChargePoint();

    void releaseChargePoint(Location loc);

}
